import './style.css'
import javascriptLogo from './javascript.svg'
import viteLogo from '/vite.svg'
import { setupCounter } from './counter.js'

document.querySelector('#app').innerHTML = `
  <div class="min-h-screen bg-gray-100 flex flex-col items-center justify-center">
    <div class="bg-white p-8 rounded-lg shadow-lg max-w-md w-full text-center">
      <div class="flex justify-center space-x-4 mb-6">
        <a href="https://vite.dev" target="_blank" class="hover:opacity-80 transition-opacity">
          <img src="${viteLogo}" class="logo h-16 w-16" alt="Vite logo" />
        </a>
        <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript" target="_blank" class="hover:opacity-80 transition-opacity">
          <img src="${javascriptLogo}" class="logo vanilla h-16 w-16" alt="JavaScript logo" />
        </a>
      </div>
      <h1 class="text-3xl font-bold text-gray-800 mb-6">Hello Vite + Tailwind!</h1>
      <div class="card mb-6">
        <button id="counter" type="button" class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded-lg transition-colors"></button>
      </div>
      <p class="text-gray-600 text-sm">
        Click on the Vite logo to learn more. Styled with Tailwind CSS!
      </p>
    </div>
  </div>
`

setupCounter(document.querySelector('#counter'))
