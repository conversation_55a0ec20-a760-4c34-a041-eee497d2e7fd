<!DOCTYPE html>
<html lang="zh-CN" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="重庆锦雨丰建筑有限公司 - 专业提供玻璃幕墙和铝合金门窗服务，致力于为客户提供高品质建筑服务的综合性企业">
    <meta name="keywords" content="重庆建筑,玻璃幕墙,铝合金门窗,建筑幕墙,门窗定制,重庆锦雨丰">
    <title>重庆锦雨丰建筑有限公司 - 专业玻璃幕墙与铝合金门窗服务</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/src/images/logo/logo-black.png">
    
    <!-- CSS -->
    <link rel="stylesheet" href="/src/style.css">
    
    <!-- 预加载关键资源 -->
    <link rel="preload" href="/src/images/banners/banners1.jpg" as="image">
    <link rel="preload" href="/src/images/logo/logo-black.png" as="image">
    <link rel="preload" href="/src/images/logo/logo-white.png" as="image">
</head>
<body class="antialiased">
    <!-- 页面加载指示器 -->
    <div id="page-loader" class="fixed inset-0 bg-white dark:bg-gray-900 z-50 flex items-center justify-center">
        <div class="text-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <p class="text-gray-600 dark:text-gray-400">加载中...</p>
        </div>
    </div>

    <!-- 主要内容 -->
    <main id="app" class="opacity-0 transition-opacity duration-500">
        <!-- Header -->
        <header class="bg-white dark:bg-gray-900 shadow-lg fixed w-full top-0 z-50 transition-colors duration-300">
            <nav class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center h-16">
                    <!-- Logo -->
                    <div class="flex-shrink-0 flex items-center">
                        <img class="h-10 w-auto dark:hidden" src="/src/images/logo/logo-black.png" alt="重庆锦雨丰建筑有限公司">
                        <img class="h-10 w-auto hidden dark:block" src="/src/images/logo/logo-white.png" alt="重庆锦雨丰建筑有限公司">
                        <span class="text-xl font-bold text-gray-900 dark:text-white hidden sm:block ml-3">
                            重庆锦雨丰建筑有限公司
                        </span>
                    </div>

                    <!-- Desktop Navigation -->
                    <div class="hidden md:block">
                        <div class="ml-10 flex items-baseline space-x-4">
                            <a href="#" class="text-primary-600 dark:text-primary-400 px-3 py-2 rounded-md text-sm font-medium">网站首页</a>
                            <a href="#about" class="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200">关于我们</a>
                            <a href="#products" class="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200">我们的产品</a>
                            <a href="#cases" class="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200">成功案例</a>
                            <a href="#videos" class="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200">视频中心</a>
                            <a href="#news" class="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200">资讯动态</a>
                            <a href="#contact" class="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200">联系我们</a>
                        </div>
                    </div>

                    <!-- Right side buttons -->
                    <div class="flex items-center space-x-4">
                        <!-- Theme Toggle -->
                        <button 
                            id="theme-toggle"
                            class="p-2 rounded-md text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200"
                            aria-label="切换主题"
                        >
                            <svg id="sun-icon" class="h-5 w-5 hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                            </svg>
                            <svg id="moon-icon" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                            </svg>
                        </button>

                        <!-- Contact Button -->
                        <a href="#contact" class="hidden md:inline-flex bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200">
                            联系我们
                        </a>

                        <!-- Mobile menu button -->
                        <button 
                            id="mobile-menu-button"
                            class="md:hidden p-2 rounded-md text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200"
                            aria-label="打开菜单"
                        >
                            <svg id="menu-icon" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                            </svg>
                            <svg id="close-icon" class="h-6 w-6 hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Mobile Navigation Menu -->
                <div id="mobile-menu" class="md:hidden hidden">
                    <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700">
                        <a href="#" class="text-primary-600 dark:text-primary-400 block px-3 py-2 rounded-md text-base font-medium">网站首页</a>
                        <a href="#about" class="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200">关于我们</a>
                        <a href="#products" class="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200">我们的产品</a>
                        <a href="#cases" class="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200">成功案例</a>
                        <a href="#videos" class="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200">视频中心</a>
                        <a href="#news" class="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200">资讯动态</a>
                        <a href="#contact" class="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200">联系我们</a>
                        <a href="#contact" class="block w-full text-center bg-primary-600 hover:bg-primary-700 text-white px-3 py-2 rounded-md text-base font-medium mt-4 transition-colors duration-200">
                            联系我们
                        </a>
                    </div>
                </div>
            </nav>
        </header>

        <!-- 主要内容区域 -->
        <div class="pt-16">
            <!-- Hero Section -->
            <section class="relative h-screen overflow-hidden">
                <div class="relative h-full">
                    <!-- Slide 1 -->
                    <div id="slide-0" class="absolute inset-0 transition-opacity duration-1000 opacity-100">
                        <div class="absolute inset-0 bg-black bg-opacity-40 z-10"></div>
                        <img 
                            src="/src/images/banners/banners1.jpg" 
                            alt="专业玻璃幕墙设计与施工"
                            class="w-full h-full object-cover"
                        >
                        <div class="absolute inset-0 flex items-center justify-center z-20">
                            <div class="text-center text-white max-w-4xl mx-auto px-4">
                                <h1 class="text-4xl md:text-6xl font-bold mb-6 animate-fade-in-up">
                                    专业玻璃幕墙设计与施工
                                </h1>
                                <p class="text-xl md:text-2xl mb-8 opacity-90 animate-fade-in-up" style="animation-delay: 0.2s;">
                                    打造现代化建筑外观，提升建筑品质
                                </p>
                                <div class="animate-fade-in-up" style="animation-delay: 0.4s;">
                                    <a 
                                        href="#about" 
                                        class="inline-flex items-center bg-primary-600 hover:bg-primary-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
                                    >
                                        了解更多
                                        <svg class="ml-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Slide 2 -->
                    <div id="slide-1" class="absolute inset-0 transition-opacity duration-1000 opacity-0">
                        <div class="absolute inset-0 bg-black bg-opacity-40 z-10"></div>
                        <img 
                            src="/src/images/banners/banners2.jpg" 
                            alt="高品质铝合金门窗定制"
                            class="w-full h-full object-cover"
                        >
                        <div class="absolute inset-0 flex items-center justify-center z-20">
                            <div class="text-center text-white max-w-4xl mx-auto px-4">
                                <h1 class="text-4xl md:text-6xl font-bold mb-6">
                                    高品质铝合金门窗定制
                                </h1>
                                <p class="text-xl md:text-2xl mb-8 opacity-90">
                                    节能环保，安全耐用，为您打造舒适家居
                                </p>
                                <div>
                                    <a 
                                        href="#products" 
                                        class="inline-flex items-center bg-primary-600 hover:bg-primary-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
                                    >
                                        查看产品
                                        <svg class="ml-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Slide 3 -->
                    <div id="slide-2" class="absolute inset-0 transition-opacity duration-1000 opacity-0">
                        <div class="absolute inset-0 bg-black bg-opacity-40 z-10"></div>
                        <img 
                            src="/src/images/banners/banners3.jpg" 
                            alt="丰富的成功案例经验"
                            class="w-full h-full object-cover"
                        >
                        <div class="absolute inset-0 flex items-center justify-center z-20">
                            <div class="text-center text-white max-w-4xl mx-auto px-4">
                                <h1 class="text-4xl md:text-6xl font-bold mb-6">
                                    丰富的成功案例经验
                                </h1>
                                <p class="text-xl md:text-2xl mb-8 opacity-90">
                                    多年行业经验，值得信赖的建筑合作伙伴
                                </p>
                                <div>
                                    <a 
                                        href="#cases" 
                                        class="inline-flex items-center bg-primary-600 hover:bg-primary-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
                                    >
                                        查看案例
                                        <svg class="ml-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 导航点 -->
                <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-30">
                    <div class="flex space-x-3">
                        <button class="w-3 h-3 rounded-full bg-white transition-all duration-300" data-slide="0"></button>
                        <button class="w-3 h-3 rounded-full bg-white bg-opacity-50 transition-all duration-300" data-slide="1"></button>
                        <button class="w-3 h-3 rounded-full bg-white bg-opacity-50 transition-all duration-300" data-slide="2"></button>
                    </div>
                </div>

                <!-- 左右箭头 -->
                <button id="prev-slide" class="absolute left-4 top-1/2 transform -translate-y-1/2 z-30 bg-black bg-opacity-30 hover:bg-opacity-50 text-white p-3 rounded-full transition-all duration-300 group">
                    <svg class="h-6 w-6 transform group-hover:-translate-x-1 transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                    </svg>
                </button>
                
                <button id="next-slide" class="absolute right-4 top-1/2 transform -translate-y-1/2 z-30 bg-black bg-opacity-30 hover:bg-opacity-50 text-white p-3 rounded-full transition-all duration-300 group">
                    <svg class="h-6 w-6 transform group-hover:translate-x-1 transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </button>

                <!-- 滚动提示 -->
                <div class="absolute bottom-8 right-8 z-30 text-white animate-bounce">
                    <div class="flex flex-col items-center">
                        <span class="text-sm mb-2 opacity-80">向下滚动</span>
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                        </svg>
                    </div>
                </div>
            </section>
        </div>

        <!-- 回到顶部按钮 -->
        <button 
            id="scroll-to-top"
            class="fixed bottom-8 right-8 bg-primary-600 hover:bg-primary-700 text-white p-3 rounded-full shadow-lg opacity-0 invisible transition-all duration-300 z-50 group"
            aria-label="回到顶部"
        >
            <svg class="h-6 w-6 transform group-hover:-translate-y-1 transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
            </svg>
        </button>
    </main>

    <!-- JavaScript -->
    <script src="/js/theme.js"></script>
    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            const app = document.getElementById('app');
            const loader = document.getElementById('page-loader');
            
            // 轮播图功能
            let currentSlide = 0;
            const totalSlides = 3;
            let autoPlayInterval;

            function showSlide(index) {
                // 隐藏所有幻灯片
                for (let i = 0; i < totalSlides; i++) {
                    const slide = document.getElementById(`slide-${i}`);
                    const dot = document.querySelector(`[data-slide="${i}"]`);
                    if (slide && dot) {
                        slide.classList.remove('opacity-100');
                        slide.classList.add('opacity-0');
                        dot.classList.remove('bg-white');
                        dot.classList.add('bg-opacity-50');
                    }
                }
                
                // 显示当前幻灯片
                const currentSlideEl = document.getElementById(`slide-${index}`);
                const currentDot = document.querySelector(`[data-slide="${index}"]`);
                if (currentSlideEl && currentDot) {
                    currentSlideEl.classList.remove('opacity-0');
                    currentSlideEl.classList.add('opacity-100');
                    currentDot.classList.remove('bg-opacity-50');
                    currentDot.classList.add('bg-white');
                }
                
                currentSlide = index;
            }

            function nextSlide() {
                const next = (currentSlide + 1) % totalSlides;
                showSlide(next);
            }

            function prevSlide() {
                const prev = (currentSlide - 1 + totalSlides) % totalSlides;
                showSlide(prev);
            }

            function startAutoPlay() {
                autoPlayInterval = setInterval(nextSlide, 5000);
            }

            function stopAutoPlay() {
                if (autoPlayInterval) {
                    clearInterval(autoPlayInterval);
                }
            }

            // 绑定轮播图事件
            document.getElementById('next-slide').addEventListener('click', () => {
                stopAutoPlay();
                nextSlide();
                startAutoPlay();
            });

            document.getElementById('prev-slide').addEventListener('click', () => {
                stopAutoPlay();
                prevSlide();
                startAutoPlay();
            });

            // 导航点点击
            document.querySelectorAll('[data-slide]').forEach((dot, index) => {
                dot.addEventListener('click', () => {
                    stopAutoPlay();
                    showSlide(index);
                    startAutoPlay();
                });
            });

            // 键盘导航
            document.addEventListener('keydown', (e) => {
                if (e.key === 'ArrowLeft') {
                    stopAutoPlay();
                    prevSlide();
                    startAutoPlay();
                } else if (e.key === 'ArrowRight') {
                    stopAutoPlay();
                    nextSlide();
                    startAutoPlay();
                }
            });

            // 启动自动播放
            startAutoPlay();

            // 主题切换功能
            function initTheme() {
                const themeToggle = document.getElementById('theme-toggle');
                const sunIcon = document.getElementById('sun-icon');
                const moonIcon = document.getElementById('moon-icon');
                const html = document.documentElement;
                
                function getCurrentTheme() {
                    return localStorage.getItem('theme') || 
                           (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                }
                
                function applyTheme(theme) {
                    if (theme === 'dark') {
                        html.classList.add('dark');
                        sunIcon.classList.remove('hidden');
                        moonIcon.classList.add('hidden');
                    } else {
                        html.classList.remove('dark');
                        sunIcon.classList.add('hidden');
                        moonIcon.classList.remove('hidden');
                    }
                    localStorage.setItem('theme', theme);
                }
                
                function toggleTheme() {
                    const currentTheme = getCurrentTheme();
                    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
                    applyTheme(newTheme);
                }
                
                applyTheme(getCurrentTheme());
                themeToggle.addEventListener('click', toggleTheme);
                
                window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
                    if (!localStorage.getItem('theme')) {
                        applyTheme(e.matches ? 'dark' : 'light');
                    }
                });
            }
            
            initTheme();

            // 移动端菜单
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');
            const menuIcon = document.getElementById('menu-icon');
            const closeIcon = document.getElementById('close-icon');
            let isMenuOpen = false;

            mobileMenuButton.addEventListener('click', () => {
                isMenuOpen = !isMenuOpen;
                
                if (isMenuOpen) {
                    mobileMenu.classList.remove('hidden');
                    menuIcon.classList.add('hidden');
                    closeIcon.classList.remove('hidden');
                } else {
                    mobileMenu.classList.add('hidden');
                    menuIcon.classList.remove('hidden');
                    closeIcon.classList.add('hidden');
                }
            });

            // 平滑滚动
            document.addEventListener('click', (e) => {
                const link = e.target.closest('a[href^="#"]');
                if (link) {
                    e.preventDefault();
                    const targetId = link.getAttribute('href').substring(1);
                    const targetElement = document.getElementById(targetId);
                    if (targetElement) {
                        targetElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                        
                        // 关闭移动端菜单
                        if (isMenuOpen) {
                            isMenuOpen = false;
                            mobileMenu.classList.add('hidden');
                            menuIcon.classList.remove('hidden');
                            closeIcon.classList.add('hidden');
                        }
                    }
                }
            });

            // 回到顶部按钮
            const scrollToTopButton = document.getElementById('scroll-to-top');
            window.addEventListener('scroll', () => {
                if (window.pageYOffset > 300) {
                    scrollToTopButton.classList.remove('opacity-0', 'invisible');
                    scrollToTopButton.classList.add('opacity-100', 'visible');
                } else {
                    scrollToTopButton.classList.add('opacity-0', 'invisible');
                    scrollToTopButton.classList.remove('opacity-100', 'visible');
                }
            });

            scrollToTopButton.addEventListener('click', () => {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
            
            // 隐藏加载器
            setTimeout(() => {
                loader.style.opacity = '0';
                app.classList.remove('opacity-0');
                setTimeout(() => loader.remove(), 500);
            }, 1000);
        });
    </script>
</body>
</html>
