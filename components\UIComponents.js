// 通用UI组件

// 卡片组件
export class Card {
  static create(content, className = '') {
    return `
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 ${className}">
        ${content}
      </div>
    `;
  }

  static createWithImage(image, title, description, link = '', className = '') {
    return `
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden group ${className}">
        <div class="relative overflow-hidden">
          <img src="${image}" alt="${title}" class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
          <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300"></div>
        </div>
        <div class="p-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-200">
            ${title}
          </h3>
          <p class="text-gray-600 dark:text-gray-300 text-sm leading-relaxed">
            ${description}
          </p>
          ${link ? `
            <div class="mt-4">
              <a href="${link}" class="inline-flex items-center text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 text-sm font-medium transition-colors duration-200">
                了解更多
                <svg class="ml-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </a>
            </div>
          ` : ''}
        </div>
      </div>
    `;
  }
}

// 按钮组件
export class Button {
  static primary(text, onClick = '', className = '') {
    return `
      <button 
        ${onClick ? `onclick="${onClick}"` : ''}
        class="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 ${className}"
      >
        ${text}
      </button>
    `;
  }

  static secondary(text, onClick = '', className = '') {
    return `
      <button 
        ${onClick ? `onclick="${onClick}"` : ''}
        class="bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-900 dark:text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 ${className}"
      >
        ${text}
      </button>
    `;
  }

  static outline(text, onClick = '', className = '') {
    return `
      <button 
        ${onClick ? `onclick="${onClick}"` : ''}
        class="border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 ${className}"
      >
        ${text}
      </button>
    `;
  }
}

// 模态框组件
export class Modal {
  static create(id, title, content, size = 'md') {
    const sizeClasses = {
      sm: 'max-w-md',
      md: 'max-w-lg',
      lg: 'max-w-2xl',
      xl: 'max-w-4xl'
    };

    return `
      <div id="${id}" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50 p-4">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl ${sizeClasses[size]} w-full max-h-screen overflow-y-auto">
          <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
              ${title}
            </h3>
            <button onclick="Modal.close('${id}')" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
              <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          <div class="p-6">
            ${content}
          </div>
        </div>
      </div>
    `;
  }

  static show(id) {
    const modal = document.getElementById(id);
    if (modal) {
      modal.classList.remove('hidden');
      modal.classList.add('flex');
      document.body.style.overflow = 'hidden';
    }
  }

  static close(id) {
    const modal = document.getElementById(id);
    if (modal) {
      modal.classList.add('hidden');
      modal.classList.remove('flex');
      document.body.style.overflow = '';
    }
  }
}

// 标签组件
export class Badge {
  static create(text, type = 'default', className = '') {
    const typeClasses = {
      default: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
      primary: 'bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-300',
      success: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      warning: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
      error: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
    };

    return `
      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${typeClasses[type]} ${className}">
        ${text}
      </span>
    `;
  }
}

// 分页组件
export class Pagination {
  static create(currentPage, totalPages, baseUrl = '') {
    if (totalPages <= 1) return '';

    const pages = [];
    const showPages = 5; // 显示的页码数量
    
    let startPage = Math.max(1, currentPage - Math.floor(showPages / 2));
    let endPage = Math.min(totalPages, startPage + showPages - 1);
    
    if (endPage - startPage + 1 < showPages) {
      startPage = Math.max(1, endPage - showPages + 1);
    }

    // 上一页
    pages.push(`
      <a href="${baseUrl}?page=${currentPage - 1}" 
         class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 ${currentPage === 1 ? 'cursor-not-allowed opacity-50' : ''}"
         ${currentPage === 1 ? 'onclick="return false"' : ''}
      >
        <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
        </svg>
      </a>
    `);

    // 页码
    for (let i = startPage; i <= endPage; i++) {
      pages.push(`
        <a href="${baseUrl}?page=${i}" 
           class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium ${
             i === currentPage 
               ? 'bg-primary-600 text-white border-primary-600' 
               : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
           }"
        >
          ${i}
        </a>
      `);
    }

    // 下一页
    pages.push(`
      <a href="${baseUrl}?page=${currentPage + 1}" 
         class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 ${currentPage === totalPages ? 'cursor-not-allowed opacity-50' : ''}"
         ${currentPage === totalPages ? 'onclick="return false"' : ''}
      >
        <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
        </svg>
      </a>
    `);

    return `
      <nav class="flex items-center justify-center">
        <div class="flex -space-x-px">
          ${pages.join('')}
        </div>
      </nav>
    `;
  }
}

// 搜索框组件
export class SearchBox {
  static create(placeholder = '搜索...', onSearch = '', className = '') {
    return `
      <div class="relative ${className}">
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
        <input 
          type="text" 
          placeholder="${placeholder}"
          ${onSearch ? `onkeyup="${onSearch}"` : ''}
          class="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
        >
      </div>
    `;
  }
}

// 加载骨架屏组件
export class Skeleton {
  static card(count = 1) {
    return Array(count).fill().map(() => `
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 animate-pulse">
        <div class="h-48 bg-gray-300 dark:bg-gray-600 rounded mb-4"></div>
        <div class="h-4 bg-gray-300 dark:bg-gray-600 rounded mb-2"></div>
        <div class="h-4 bg-gray-300 dark:bg-gray-600 rounded w-3/4 mb-2"></div>
        <div class="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/2"></div>
      </div>
    `).join('');
  }

  static text(lines = 3) {
    return Array(lines).fill().map((_, index) => `
      <div class="h-4 bg-gray-300 dark:bg-gray-600 rounded animate-pulse mb-2 ${index === lines - 1 ? 'w-3/4' : 'w-full'}"></div>
    `).join('');
  }
}
