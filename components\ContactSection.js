// 联系咨询部分组件
import { siteData } from '../data/siteData.js';

export class ContactSection {
  constructor() {
    this.init();
  }

  init() {
    this.render();
    this.setupEventListeners();
  }

  render() {
    const contactHTML = `
      <section class="py-20 bg-primary-600 dark:bg-primary-800 text-white relative overflow-hidden">
        <!-- 背景装饰 -->
        <div class="absolute inset-0 opacity-10">
          <div class="absolute top-0 left-0 w-64 h-64 bg-white rounded-full -translate-x-32 -translate-y-32"></div>
          <div class="absolute bottom-0 right-0 w-96 h-96 bg-white rounded-full translate-x-48 translate-y-48"></div>
        </div>
        
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <!-- 左侧：联系信息 -->
            <div>
              <h2 class="text-3xl md:text-4xl font-bold mb-6">
                联系我们
              </h2>
              <p class="text-xl mb-8 opacity-90">
                专业的建筑服务团队，随时为您提供咨询和支持
              </p>
              
              <div class="space-y-6">
                <div class="flex items-center space-x-4">
                  <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                  </div>
                  <div>
                    <h4 class="font-semibold text-lg">电话咨询</h4>
                    <p class="opacity-90">${siteData.company.phone}</p>
                  </div>
                </div>
                
                <div class="flex items-center space-x-4">
                  <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M6 6h12a2 2 0 012 2v8a2 2 0 01-2 2H6a2 2 0 01-2-2V8a2 2 0 012-2z" />
                    </svg>
                  </div>
                  <div>
                    <h4 class="font-semibold text-lg">手机联系</h4>
                    <p class="opacity-90">${siteData.company.mobile}</p>
                  </div>
                </div>
                
                <div class="flex items-center space-x-4">
                  <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div>
                    <h4 class="font-semibold text-lg">邮箱联系</h4>
                    <p class="opacity-90">${siteData.company.email}</p>
                  </div>
                </div>
                
                <div class="flex items-start space-x-4">
                  <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </div>
                  <div>
                    <h4 class="font-semibold text-lg">公司地址</h4>
                    <p class="opacity-90">${siteData.company.address}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 右侧：联系表单 -->
            <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-8">
              <h3 class="text-2xl font-bold mb-6">在线咨询</h3>
              <form id="contact-form" class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label for="name" class="block text-sm font-medium mb-2">姓名 *</label>
                    <input 
                      type="text" 
                      id="name" 
                      name="name" 
                      required
                      class="w-full px-4 py-3 bg-white bg-opacity-20 border border-white border-opacity-30 rounded-lg text-white placeholder-white placeholder-opacity-70 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 focus:border-transparent"
                      placeholder="请输入您的姓名"
                    >
                  </div>
                  <div>
                    <label for="phone" class="block text-sm font-medium mb-2">电话 *</label>
                    <input 
                      type="tel" 
                      id="phone" 
                      name="phone" 
                      required
                      class="w-full px-4 py-3 bg-white bg-opacity-20 border border-white border-opacity-30 rounded-lg text-white placeholder-white placeholder-opacity-70 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 focus:border-transparent"
                      placeholder="请输入您的电话"
                    >
                  </div>
                </div>
                
                <div>
                  <label for="email" class="block text-sm font-medium mb-2">邮箱</label>
                  <input 
                    type="email" 
                    id="email" 
                    name="email"
                    class="w-full px-4 py-3 bg-white bg-opacity-20 border border-white border-opacity-30 rounded-lg text-white placeholder-white placeholder-opacity-70 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 focus:border-transparent"
                    placeholder="请输入您的邮箱"
                  >
                </div>
                
                <div>
                  <label for="service" class="block text-sm font-medium mb-2">咨询服务</label>
                  <select 
                    id="service" 
                    name="service"
                    class="w-full px-4 py-3 bg-white bg-opacity-20 border border-white border-opacity-30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 focus:border-transparent"
                  >
                    <option value="" class="text-gray-900">请选择咨询服务</option>
                    <option value="glass-curtain-wall" class="text-gray-900">玻璃幕墙</option>
                    <option value="aluminium-window" class="text-gray-900">铝合金门窗</option>
                    <option value="consultation" class="text-gray-900">技术咨询</option>
                    <option value="other" class="text-gray-900">其他服务</option>
                  </select>
                </div>
                
                <div>
                  <label for="message" class="block text-sm font-medium mb-2">留言内容 *</label>
                  <textarea 
                    id="message" 
                    name="message" 
                    rows="4" 
                    required
                    class="w-full px-4 py-3 bg-white bg-opacity-20 border border-white border-opacity-30 rounded-lg text-white placeholder-white placeholder-opacity-70 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 focus:border-transparent resize-none"
                    placeholder="请详细描述您的需求..."
                  ></textarea>
                </div>
                
                <button 
                  type="submit"
                  class="w-full bg-white text-primary-600 px-6 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
                >
                  提交咨询
                </button>
              </form>
            </div>
          </div>
        </div>
      </section>
    `;

    return contactHTML;
  }

  setupEventListeners() {
    document.addEventListener('submit', (e) => {
      if (e.target.id === 'contact-form') {
        e.preventDefault();
        this.handleFormSubmit(e.target);
      }
    });
  }

  handleFormSubmit(form) {
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);
    
    // 简单的表单验证
    if (!data.name || !data.phone || !data.message) {
      this.showMessage('请填写必填项', 'error');
      return;
    }
    
    // 电话号码验证
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(data.phone)) {
      this.showMessage('请输入正确的手机号码', 'error');
      return;
    }
    
    // 邮箱验证（如果填写了）
    if (data.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(data.email)) {
        this.showMessage('请输入正确的邮箱地址', 'error');
        return;
      }
    }
    
    // 模拟提交
    this.showMessage('正在提交...', 'info');
    
    setTimeout(() => {
      this.showMessage('提交成功！我们会尽快与您联系', 'success');
      form.reset();
    }, 1500);
  }

  showMessage(message, type) {
    // 创建消息提示
    const messageId = 'message-' + Date.now();
    const typeClasses = {
      success: 'bg-green-500 text-white',
      error: 'bg-red-500 text-white',
      warning: 'bg-yellow-500 text-white',
      info: 'bg-blue-500 text-white'
    };

    const messageHTML = `
      <div id="${messageId}" class="fixed top-20 right-4 ${typeClasses[type]} px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300">
        <div class="flex items-center space-x-2">
          <span>${message}</span>
          <button onclick="document.getElementById('${messageId}').remove()" class="ml-2 text-white hover:text-gray-200">
            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
    `;

    document.body.insertAdjacentHTML('beforeend', messageHTML);
    
    const messageElement = document.getElementById(messageId);
    
    // 显示动画
    setTimeout(() => {
      messageElement.classList.remove('translate-x-full');
    }, 100);

    // 自动隐藏
    setTimeout(() => {
      messageElement.classList.add('translate-x-full');
      setTimeout(() => {
        messageElement.remove();
      }, 300);
    }, 3000);
  }
}
