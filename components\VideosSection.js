// 视频中心部分组件
import { videosData } from '../data/contentData.js';
import { Modal } from './UIComponents.js';

export class VideosSection {
  constructor() {
    this.init();
  }

  init() {
    this.render();
    this.setupEventListeners();
  }

  render() {
    const videosHTML = `
      <section class="py-20 bg-gray-50 dark:bg-gray-800 transition-colors duration-300">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <!-- 标题部分 -->
          <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              视频中心
            </h2>
            <div class="w-20 h-1 bg-primary-600 mx-auto mb-6"></div>
            <p class="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              通过视频了解我们的专业实力、施工工艺和成功案例
            </p>
          </div>

          <!-- 视频展示 -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            ${videosData.slice(0, 6).map(video => `
              <div class="group bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden transform hover:-translate-y-2">
                <div class="relative overflow-hidden cursor-pointer" onclick="VideosSection.playVideo('${video.videoUrl}', '${video.title}')">
                  <img 
                    src="${video.thumbnail}" 
                    alt="${video.title}"
                    class="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-700"
                  >
                  
                  <!-- 播放按钮覆盖层 -->
                  <div class="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center group-hover:bg-opacity-50 transition-all duration-500">
                    <div class="w-16 h-16 bg-white bg-opacity-90 rounded-full flex items-center justify-center transform group-hover:scale-110 transition-all duration-300">
                      <svg class="w-6 h-6 text-primary-600 ml-1" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M8 5v14l11-7z"/>
                      </svg>
                    </div>
                  </div>
                  
                  <!-- 时长标签 -->
                  <div class="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white px-2 py-1 rounded text-sm">
                    ${video.duration}
                  </div>
                  
                  <!-- 分类标签 -->
                  <div class="absolute top-2 left-2">
                    <span class="px-2 py-1 bg-primary-600 text-white text-xs rounded-full font-medium">
                      ${video.category}
                    </span>
                  </div>
                </div>
                
                <div class="p-6">
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-300">
                    <a href="/videos/${video.id}" class="hover:underline">
                      ${video.title}
                    </a>
                  </h3>
                  
                  <p class="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-2">
                    ${video.description}
                  </p>
                  
                  <div class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                    <div class="flex items-center space-x-4">
                      <span class="flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        ${video.views}
                      </span>
                      <span class="flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        ${video.publishDate}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            `).join('')}
          </div>

          <!-- 查看更多按钮 -->
          <div class="text-center">
            <a 
              href="/videos" 
              class="inline-flex items-center bg-primary-600 hover:bg-primary-700 text-white px-8 py-4 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
            >
              查看更多视频
              <svg class="ml-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </a>
          </div>
        </div>

        <!-- 视频播放模态框 -->
        <div id="video-modal" class="fixed inset-0 bg-black bg-opacity-75 hidden items-center justify-center z-50 p-4">
          <div class="relative max-w-4xl w-full">
            <button 
              onclick="VideosSection.closeVideo()" 
              class="absolute -top-12 right-0 text-white hover:text-gray-300 transition-colors duration-200"
            >
              <svg class="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
            <div class="bg-black rounded-lg overflow-hidden">
              <video 
                id="video-player" 
                class="w-full h-auto" 
                controls 
                preload="metadata"
              >
                您的浏览器不支持视频播放。
              </video>
            </div>
          </div>
        </div>
      </section>
    `;

    return videosHTML;
  }

  setupEventListeners() {
    // 点击模态框外部关闭视频
    document.addEventListener('click', (e) => {
      const modal = document.getElementById('video-modal');
      if (e.target === modal) {
        VideosSection.closeVideo();
      }
    });

    // ESC键关闭视频
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        VideosSection.closeVideo();
      }
    });
  }

  static playVideo(videoUrl, title) {
    const modal = document.getElementById('video-modal');
    const player = document.getElementById('video-player');
    
    if (modal && player) {
      player.src = videoUrl;
      player.load();
      modal.classList.remove('hidden');
      modal.classList.add('flex');
      document.body.style.overflow = 'hidden';
      
      // 自动播放
      player.play().catch(e => {
        console.log('自动播放失败:', e);
      });
    }
  }

  static closeVideo() {
    const modal = document.getElementById('video-modal');
    const player = document.getElementById('video-player');
    
    if (modal && player) {
      player.pause();
      player.src = '';
      modal.classList.add('hidden');
      modal.classList.remove('flex');
      document.body.style.overflow = '';
    }
  }
}
