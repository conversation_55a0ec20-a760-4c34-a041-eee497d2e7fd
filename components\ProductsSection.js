// 产品展示部分组件
export class ProductsSection {
  constructor() {
    this.init();
  }

  init() {
    this.render();
  }

  render() {
    const productsHTML = `
      <section class="py-20 bg-gray-50 dark:bg-gray-800 transition-colors duration-300">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <!-- 标题部分 -->
          <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              我们的产品
            </h2>
            <div class="w-20 h-1 bg-primary-600 mx-auto mb-6"></div>
            <p class="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              专业提供玻璃幕墙和铝合金门窗产品，以卓越的品质和创新的设计，为现代建筑提供完美的解决方案
            </p>
          </div>

          <!-- 产品展示 -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- 玻璃幕墙 -->
            <div class="group relative overflow-hidden rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
              <div class="aspect-w-16 aspect-h-12 relative">
                <img 
                  src="/src/images/glass-curtain-wall/glass-curtain-wall1.jpg" 
                  alt="玻璃幕墙"
                  class="w-full h-80 object-cover group-hover:scale-110 transition-transform duration-700"
                >
                <div class="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-60"></div>
                <div class="absolute inset-0 bg-primary-600 bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-500"></div>
              </div>
              
              <div class="absolute bottom-0 left-0 right-0 p-8 text-white">
                <div class="transform translate-y-4 group-hover:translate-y-0 transition-transform duration-500">
                  <h3 class="text-2xl font-bold mb-3">玻璃幕墙</h3>
                  <p class="text-gray-200 mb-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500 delay-100">
                    现代建筑的完美外衣，集美观、节能、安全于一体的专业幕墙系统
                  </p>
                  
                  <div class="flex flex-wrap gap-2 mb-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500 delay-200">
                    <span class="px-3 py-1 bg-white bg-opacity-20 rounded-full text-sm">框架式</span>
                    <span class="px-3 py-1 bg-white bg-opacity-20 rounded-full text-sm">单元式</span>
                    <span class="px-3 py-1 bg-white bg-opacity-20 rounded-full text-sm">点支式</span>
                  </div>
                  
                  <a 
                    href="/glass-curtain-wall" 
                    class="inline-flex items-center bg-white text-primary-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 opacity-0 group-hover:opacity-100 delay-300"
                  >
                    了解详情
                    <svg class="ml-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                  </a>
                </div>
              </div>
              
              <!-- 装饰元素 -->
              <div class="absolute top-4 right-4 w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
            </div>

            <!-- 铝合金门窗 -->
            <div class="group relative overflow-hidden rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
              <div class="aspect-w-16 aspect-h-12 relative">
                <img 
                  src="/src/images/aluminium-window/aluminium-window1.jpg" 
                  alt="铝合金门窗"
                  class="w-full h-80 object-cover group-hover:scale-110 transition-transform duration-700"
                >
                <div class="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-60"></div>
                <div class="absolute inset-0 bg-primary-600 bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-500"></div>
              </div>
              
              <div class="absolute bottom-0 left-0 right-0 p-8 text-white">
                <div class="transform translate-y-4 group-hover:translate-y-0 transition-transform duration-500">
                  <h3 class="text-2xl font-bold mb-3">铝合金门窗</h3>
                  <p class="text-gray-200 mb-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500 delay-100">
                    高品质门窗定制，为您的家居和办公空间提供安全、美观、节能的解决方案
                  </p>
                  
                  <div class="flex flex-wrap gap-2 mb-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500 delay-200">
                    <span class="px-3 py-1 bg-white bg-opacity-20 rounded-full text-sm">推拉窗</span>
                    <span class="px-3 py-1 bg-white bg-opacity-20 rounded-full text-sm">平开窗</span>
                    <span class="px-3 py-1 bg-white bg-opacity-20 rounded-full text-sm">平开门</span>
                  </div>
                  
                  <a 
                    href="/aluminium-window" 
                    class="inline-flex items-center bg-white text-primary-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 opacity-0 group-hover:opacity-100 delay-300"
                  >
                    了解详情
                    <svg class="ml-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                  </a>
                </div>
              </div>
              
              <!-- 装饰元素 -->
              <div class="absolute top-4 right-4 w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10h16v11H4V10z" />
                </svg>
              </div>
            </div>
          </div>

          <!-- 特色优势 -->
          <div class="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="text-center group">
              <div class="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-primary-600 transition-colors duration-300">
                <svg class="w-8 h-8 text-primary-600 group-hover:text-white transition-colors duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h4 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">品质保证</h4>
              <p class="text-gray-600 dark:text-gray-300">严格的质量控制体系，确保每一个产品都达到最高标准</p>
            </div>
            
            <div class="text-center group">
              <div class="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-primary-600 transition-colors duration-300">
                <svg class="w-8 h-8 text-primary-600 group-hover:text-white transition-colors duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h4 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">节能环保</h4>
              <p class="text-gray-600 dark:text-gray-300">采用先进的节能技术，为建筑提供优异的保温隔热性能</p>
            </div>
            
            <div class="text-center group">
              <div class="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-primary-600 transition-colors duration-300">
                <svg class="w-8 h-8 text-primary-600 group-hover:text-white transition-colors duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 3v6m0 6v6m6-12h-6m-6 0h6" />
                </svg>
              </div>
              <h4 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">定制服务</h4>
              <p class="text-gray-600 dark:text-gray-300">根据客户需求提供个性化定制服务，满足不同项目要求</p>
            </div>
          </div>
        </div>
      </section>
    `;

    return productsHTML;
  }
}
