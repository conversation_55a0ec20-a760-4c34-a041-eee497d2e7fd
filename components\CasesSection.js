// 成功案例部分组件
import { casesData } from '../data/contentData.js';
import { Card } from './UIComponents.js';

export class CasesSection {
  constructor() {
    this.init();
  }

  init() {
    this.render();
  }

  render() {
    const casesHTML = `
      <section class="py-20 bg-white dark:bg-gray-900 transition-colors duration-300">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <!-- 标题部分 -->
          <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              成功案例
            </h2>
            <div class="w-20 h-1 bg-primary-600 mx-auto mb-6"></div>
            <p class="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              多年来，我们为众多客户提供了优质的建筑服务，积累了丰富的项目经验
            </p>
          </div>

          <!-- 案例展示 -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            ${casesData.slice(0, 6).map(caseItem => `
              <div class="group bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden transform hover:-translate-y-2">
                <div class="relative overflow-hidden">
                  <img 
                    src="${caseItem.image}" 
                    alt="${caseItem.title}"
                    class="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-700"
                  >
                  <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-500"></div>
                  
                  <!-- 分类标签 -->
                  <div class="absolute top-4 left-4">
                    <span class="px-3 py-1 bg-primary-600 text-white text-sm rounded-full font-medium">
                      ${caseItem.category}
                    </span>
                  </div>
                  
                  <!-- 查看详情按钮 -->
                  <div class="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                    <a 
                      href="/cases/${caseItem.id}" 
                      class="bg-white text-primary-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105"
                    >
                      查看详情
                    </a>
                  </div>
                </div>
                
                <div class="p-6">
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-300">
                    ${caseItem.title}
                  </h3>
                  
                  <p class="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-2">
                    ${caseItem.description}
                  </p>
                  
                  <div class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-4">
                    <div class="flex items-center space-x-4">
                      <span class="flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        ${caseItem.location}
                      </span>
                      <span class="flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" />
                        </svg>
                        ${caseItem.area}
                      </span>
                    </div>
                  </div>
                  
                  <!-- 标签 -->
                  <div class="flex flex-wrap gap-2">
                    ${caseItem.tags.slice(0, 3).map(tag => `
                      <span class="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-xs rounded-md">
                        ${tag}
                      </span>
                    `).join('')}
                  </div>
                </div>
              </div>
            `).join('')}
          </div>

          <!-- 查看更多按钮 -->
          <div class="text-center">
            <a 
              href="/cases" 
              class="inline-flex items-center bg-primary-600 hover:bg-primary-700 text-white px-8 py-4 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
            >
              查看更多案例
              <svg class="ml-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </a>
          </div>
        </div>
      </section>
    `;

    return casesHTML;
  }
}
