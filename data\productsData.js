// 玻璃幕墙数据
export const glassCurtainWallData = {
  title: "玻璃幕墙",
  description: "专业的玻璃幕墙设计、制造与安装服务，为现代建筑提供优质的外围护结构解决方案。",
  
  types: [
    {
      name: "框架式幕墙",
      description: "采用铝合金型材作为框架，玻璃嵌入框架内的传统幕墙形式",
      image: "/src/images/glass-curtain-wall/glass-curtain-wall1.jpg",
      features: ["结构简单", "成本较低", "维护方便", "适用性广"]
    },
    {
      name: "单元式幕墙",
      description: "将幕墙分割成标准单元，在工厂预制后现场安装",
      image: "/src/images/glass-curtain-wall/glass-curtain-wall2.jpg",
      features: ["工厂预制", "质量可控", "安装快速", "密封性好"]
    },
    {
      name: "点支式幕墙",
      description: "通过点连接件将玻璃固定在支承结构上的幕墙形式",
      image: "/src/images/glass-curtain-wall/glass-curtain-wall3.jpg",
      features: ["通透性好", "视觉效果佳", "结构轻巧", "现代感强"]
    }
  ],

  advantages: [
    {
      title: "精密设计",
      description: "采用先进的设计软件和计算方法，确保结构安全可靠",
      icon: "AcademicCapIcon"
    },
    {
      title: "工厂预制",
      description: "标准化生产流程，严格的质量控制体系",
      icon: "CogIcon"
    },
    {
      title: "节能环保",
      description: "采用节能玻璃和保温材料，提高建筑能效",
      icon: "SparklesIcon"
    },
    {
      title: "安全可靠",
      description: "严格按照国家标准设计施工，确保使用安全",
      icon: "ShieldCheckIcon"
    },
    {
      title: "智能制造",
      description: "引入智能化生产设备，提高生产效率和产品质量",
      icon: "CpuChipIcon"
    },
    {
      title: "专业安装",
      description: "经验丰富的安装团队，确保安装质量和进度",
      icon: "WrenchScrewdriverIcon"
    }
  ],

  applications: [
    { name: "商业建筑", icon: "BuildingOfficeIcon" },
    { name: "办公楼宇", icon: "BuildingOffice2Icon" },
    { name: "酒店建筑", icon: "HomeModernIcon" },
    { name: "文化建筑", icon: "AcademicCapIcon" },
    { name: "医疗建筑", icon: "HeartIcon" },
    { name: "教育建筑", icon: "BookOpenIcon" }
  ],

  performance: [
    { name: "抗风压性能", value: "≥4.0kPa", description: "优异的抗风压能力" },
    { name: "水密性能", value: "≥600Pa", description: "良好的防水密封性" },
    { name: "保温性能", value: "K≤2.5W/(m²·K)", description: "优秀的保温隔热效果" },
    { name: "隔音性能", value: "≥35dB", description: "有效的隔音降噪" }
  ],

  serviceProcess: [
    { step: 1, title: "需求沟通", description: "深入了解客户需求和项目要求" },
    { step: 2, title: "方案设计", description: "制定详细的设计方案和技术规范" },
    { step: 3, title: "生产制造", description: "工厂化生产，严格质量控制" },
    { step: 4, title: "安装调试", description: "专业安装团队现场施工调试" }
  ]
};

// 铝合金门窗数据
export const aluminiumWindowData = {
  title: "铝合金门窗",
  description: "高品质铝合金门窗产品，集美观、实用、节能于一体，为您打造舒适安全的居住环境。",
  
  features: [
    {
      title: "坚固耐用",
      description: "采用优质铝合金型材，强度高，使用寿命长",
      icon: "ShieldCheckIcon"
    },
    {
      title: "隔热保温",
      description: "断桥隔热技术，有效阻断热传导",
      icon: "FireIcon"
    },
    {
      title: "密封防水",
      description: "多道密封设计，防水防风性能优异",
      icon: "ShieldExclamationIcon"
    },
    {
      title: "美观大方",
      description: "多种颜色和款式可选，提升建筑美观度",
      icon: "EyeIcon"
    }
  ],

  types: [
    {
      category: "窗类产品",
      items: [
        { name: "推拉窗", image: "/src/images/aluminium-window/aluminium-window1.jpg" },
        { name: "平开窗", image: "/src/images/aluminium-window/aluminium-window2.jpg" },
        { name: "上悬窗", image: "/src/images/aluminium-window/aluminium-window3.jpg" }
      ]
    },
    {
      category: "门类产品",
      items: [
        { name: "折叠门", image: "/src/images/aluminium-window/aluminium-window4.jpg" },
        { name: "平开门", image: "/src/images/aluminium-window/aluminium-window5.jpg" },
        { name: "推拉门", image: "/src/images/aluminium-window/aluminium-window6.jpg" }
      ]
    }
  ],

  performance: [
    { name: "抗风压性能", value: "≥3.0kPa", description: "抵御强风侵袭" },
    { name: "水密性能", value: "≥250Pa", description: "有效防止雨水渗透" },
    { name: "保温性能", value: "K≤2.0W/(m²·K)", description: "优异的保温效果" },
    { name: "隔音性能", value: "≥30dB", description: "营造安静环境" }
  ],

  manufacturingProcess: [
    { step: 1, title: "材料准备", description: "精选优质铝合金型材和配件" },
    { step: 2, title: "精密加工", description: "采用先进设备进行精密切割和加工" },
    { step: 3, title: "组装调试", description: "严格按照工艺要求进行组装" },
    { step: 4, title: "质量检测", description: "全面检测产品质量和性能指标" }
  ],

  applications: [
    { name: "住宅建筑", icon: "HomeIcon" },
    { name: "商业建筑", icon: "BuildingStorefrontIcon" },
    { name: "办公建筑", icon: "BuildingOfficeIcon" },
    { name: "公共建筑", icon: "BuildingLibraryIcon" },
    { name: "工业建筑", icon: "BuildingOffice2Icon" },
    { name: "酒店建筑", icon: "HomeModernIcon" }
  ]
};
