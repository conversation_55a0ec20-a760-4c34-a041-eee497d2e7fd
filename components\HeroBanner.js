// 首页轮播图组件
import { bannerData } from '../data/siteData.js';

export class HeroBanner {
  constructor() {
    this.currentSlide = 0;
    this.slides = bannerData;
    this.autoPlayInterval = null;
    this.init();
  }

  init() {
    this.render();
    this.setupEventListeners();
    this.startAutoPlay();
  }

  render() {
    const bannerHTML = `
      <section class="relative h-screen overflow-hidden">
        <!-- 轮播图容器 -->
        <div class="relative h-full">
          ${this.slides.map((slide, index) => `
            <div class="absolute inset-0 transition-opacity duration-1000 ${index === 0 ? 'opacity-100' : 'opacity-0'}" data-slide="${index}">
              <div class="absolute inset-0 bg-black bg-opacity-40 z-10"></div>
              <img 
                src="${slide.image}" 
                alt="${slide.title}"
                class="w-full h-full object-cover"
              >
              <div class="absolute inset-0 flex items-center justify-center z-20">
                <div class="text-center text-white max-w-4xl mx-auto px-4">
                  <h1 class="text-4xl md:text-6xl font-bold mb-6 animate-fade-in-up">
                    ${slide.title}
                  </h1>
                  <p class="text-xl md:text-2xl mb-8 opacity-90 animate-fade-in-up" style="animation-delay: 0.2s;">
                    ${slide.subtitle}
                  </p>
                  <div class="animate-fade-in-up" style="animation-delay: 0.4s;">
                    <a 
                      href="${slide.buttonLink}" 
                      class="inline-flex items-center bg-primary-600 hover:bg-primary-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
                    >
                      ${slide.buttonText}
                      <svg class="ml-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                      </svg>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          `).join('')}
        </div>

        <!-- 导航点 -->
        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-30">
          <div class="flex space-x-3">
            ${this.slides.map((_, index) => `
              <button 
                class="w-3 h-3 rounded-full transition-all duration-300 ${index === 0 ? 'bg-white' : 'bg-white bg-opacity-50'}"
                data-slide-to="${index}"
                aria-label="切换到第${index + 1}张图片"
              ></button>
            `).join('')}
          </div>
        </div>

        <!-- 左右箭头 -->
        <button 
          class="absolute left-4 top-1/2 transform -translate-y-1/2 z-30 bg-black bg-opacity-30 hover:bg-opacity-50 text-white p-3 rounded-full transition-all duration-300 group"
          data-slide-prev
          aria-label="上一张图片"
        >
          <svg class="h-6 w-6 transform group-hover:-translate-x-1 transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        
        <button 
          class="absolute right-4 top-1/2 transform -translate-y-1/2 z-30 bg-black bg-opacity-30 hover:bg-opacity-50 text-white p-3 rounded-full transition-all duration-300 group"
          data-slide-next
          aria-label="下一张图片"
        >
          <svg class="h-6 w-6 transform group-hover:translate-x-1 transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </button>

        <!-- 滚动提示 -->
        <div class="absolute bottom-8 right-8 z-30 text-white animate-bounce">
          <div class="flex flex-col items-center">
            <span class="text-sm mb-2 opacity-80">向下滚动</span>
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
            </svg>
          </div>
        </div>
      </section>
    `;

    return bannerHTML;
  }

  setupEventListeners() {
    // 导航点点击
    document.addEventListener('click', (e) => {
      if (e.target.hasAttribute('data-slide-to')) {
        const slideIndex = parseInt(e.target.getAttribute('data-slide-to'));
        this.goToSlide(slideIndex);
      }
    });

    // 左右箭头点击
    document.addEventListener('click', (e) => {
      if (e.target.closest('[data-slide-prev]')) {
        this.previousSlide();
      } else if (e.target.closest('[data-slide-next]')) {
        this.nextSlide();
      }
    });

    // 键盘导航
    document.addEventListener('keydown', (e) => {
      if (e.key === 'ArrowLeft') {
        this.previousSlide();
      } else if (e.key === 'ArrowRight') {
        this.nextSlide();
      }
    });

    // 鼠标悬停暂停自动播放
    const bannerSection = document.querySelector('section');
    if (bannerSection) {
      bannerSection.addEventListener('mouseenter', () => {
        this.stopAutoPlay();
      });

      bannerSection.addEventListener('mouseleave', () => {
        this.startAutoPlay();
      });
    }

    // 触摸滑动支持
    let startX = 0;
    let endX = 0;

    document.addEventListener('touchstart', (e) => {
      startX = e.touches[0].clientX;
    });

    document.addEventListener('touchend', (e) => {
      endX = e.changedTouches[0].clientX;
      const diff = startX - endX;
      
      if (Math.abs(diff) > 50) { // 最小滑动距离
        if (diff > 0) {
          this.nextSlide();
        } else {
          this.previousSlide();
        }
      }
    });
  }

  goToSlide(index) {
    if (index < 0 || index >= this.slides.length) return;

    // 隐藏当前幻灯片
    const currentSlideElement = document.querySelector(`[data-slide="${this.currentSlide}"]`);
    if (currentSlideElement) {
      currentSlideElement.classList.remove('opacity-100');
      currentSlideElement.classList.add('opacity-0');
    }

    // 显示目标幻灯片
    const targetSlideElement = document.querySelector(`[data-slide="${index}"]`);
    if (targetSlideElement) {
      targetSlideElement.classList.remove('opacity-0');
      targetSlideElement.classList.add('opacity-100');
    }

    // 更新导航点
    this.updateDots(index);

    this.currentSlide = index;
  }

  nextSlide() {
    const nextIndex = (this.currentSlide + 1) % this.slides.length;
    this.goToSlide(nextIndex);
  }

  previousSlide() {
    const prevIndex = (this.currentSlide - 1 + this.slides.length) % this.slides.length;
    this.goToSlide(prevIndex);
  }

  updateDots(activeIndex) {
    const dots = document.querySelectorAll('[data-slide-to]');
    dots.forEach((dot, index) => {
      if (index === activeIndex) {
        dot.classList.remove('bg-opacity-50');
        dot.classList.add('bg-white');
      } else {
        dot.classList.add('bg-opacity-50');
        dot.classList.remove('bg-white');
      }
    });
  }

  startAutoPlay() {
    this.stopAutoPlay(); // 清除现有的定时器
    this.autoPlayInterval = setInterval(() => {
      this.nextSlide();
    }, 5000); // 5秒切换一次
  }

  stopAutoPlay() {
    if (this.autoPlayInterval) {
      clearInterval(this.autoPlayInterval);
      this.autoPlayInterval = null;
    }
  }

  // 销毁组件
  destroy() {
    this.stopAutoPlay();
  }
}
