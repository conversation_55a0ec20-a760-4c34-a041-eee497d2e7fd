// Header 导航组件
import { siteData } from '../data/siteData.js';

export class Header {
  constructor() {
    this.isMenuOpen = false;
    this.init();
  }

  init() {
    this.render();
    this.setupEventListeners();
  }

  render() {
    const headerHTML = `
      <header class="bg-white dark:bg-gray-900 shadow-lg fixed w-full top-0 z-50 transition-colors duration-300">
        <nav class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between items-center h-16">
            <!-- Logo -->
            <div class="flex-shrink-0 flex items-center">
              <a href="/" class="flex items-center space-x-3">
                <img class="h-10 w-auto dark:hidden" src="/src/images/logo/logo-black.png" alt="${siteData.company.name}">
                <img class="h-10 w-auto hidden dark:block" src="/src/images/logo/logo-white.png" alt="${siteData.company.name}">
                <span class="text-xl font-bold text-gray-900 dark:text-white hidden sm:block">
                  ${siteData.company.name}
                </span>
              </a>
            </div>

            <!-- Desktop Navigation -->
            <div class="hidden md:block">
              <div class="ml-10 flex items-baseline space-x-4">
                ${this.renderNavigationItems()}
              </div>
            </div>

            <!-- Right side buttons -->
            <div class="hidden md:flex items-center space-x-4">
              <!-- Theme Toggle -->
              <button 
                type="button" 
                data-theme-toggle
                class="p-2 rounded-md text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200"
                aria-label="切换主题"
              >
                <svg data-theme-icon="sun" class="h-5 w-5 hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
                <svg data-theme-icon="moon" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                </svg>
              </button>

              <!-- Contact Button -->
              <a href="/contact" class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200">
                联系我们
              </a>
            </div>

            <!-- Mobile menu button -->
            <div class="md:hidden flex items-center space-x-2">
              <!-- Mobile Theme Toggle -->
              <button 
                type="button" 
                data-theme-toggle
                class="p-2 rounded-md text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200"
                aria-label="切换主题"
              >
                <svg data-theme-icon="sun" class="h-5 w-5 hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
                <svg data-theme-icon="moon" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                </svg>
              </button>

              <!-- Mobile menu button -->
              <button 
                type="button" 
                id="mobile-menu-button"
                class="p-2 rounded-md text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200"
                aria-label="打开菜单"
              >
                <svg id="menu-icon" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                </svg>
                <svg id="close-icon" class="h-6 w-6 hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>

          <!-- Mobile Navigation Menu -->
          <div id="mobile-menu" class="md:hidden hidden">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700">
              ${this.renderMobileNavigationItems()}
              <a href="/contact" class="block w-full text-center bg-primary-600 hover:bg-primary-700 text-white px-3 py-2 rounded-md text-base font-medium mt-4 transition-colors duration-200">
                联系我们
              </a>
            </div>
          </div>
        </nav>
      </header>
    `;

    // 如果已存在 header，则替换；否则插入到 body 开头
    const existingHeader = document.querySelector('header');
    if (existingHeader) {
      existingHeader.outerHTML = headerHTML;
    } else {
      document.body.insertAdjacentHTML('afterbegin', headerHTML);
    }
  }

  renderNavigationItems() {
    return siteData.navigation.map(item => `
      <a href="${item.href}" 
         class="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${item.current ? 'text-primary-600 dark:text-primary-400' : ''}"
      >
        ${item.name}
      </a>
    `).join('');
  }

  renderMobileNavigationItems() {
    return siteData.navigation.map(item => `
      <a href="${item.href}" 
         class="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 ${item.current ? 'text-primary-600 dark:text-primary-400' : ''}"
      >
        ${item.name}
      </a>
    `).join('');
  }

  setupEventListeners() {
    // 移动端菜单切换
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');
    const menuIcon = document.getElementById('menu-icon');
    const closeIcon = document.getElementById('close-icon');

    if (mobileMenuButton) {
      mobileMenuButton.addEventListener('click', () => {
        this.isMenuOpen = !this.isMenuOpen;
        
        if (this.isMenuOpen) {
          mobileMenu.classList.remove('hidden');
          menuIcon.classList.add('hidden');
          closeIcon.classList.remove('hidden');
        } else {
          mobileMenu.classList.add('hidden');
          menuIcon.classList.remove('hidden');
          closeIcon.classList.add('hidden');
        }
      });
    }

    // 点击外部关闭移动端菜单
    document.addEventListener('click', (e) => {
      if (this.isMenuOpen && !e.target.closest('nav')) {
        this.isMenuOpen = false;
        mobileMenu.classList.add('hidden');
        menuIcon.classList.remove('hidden');
        closeIcon.classList.add('hidden');
      }
    });

    // 滚动时添加阴影效果
    let lastScrollY = window.scrollY;
    window.addEventListener('scroll', () => {
      const header = document.querySelector('header');
      if (window.scrollY > 10) {
        header.classList.add('shadow-lg');
      } else {
        header.classList.remove('shadow-lg');
      }
      lastScrollY = window.scrollY;
    });
  }

  // 更新当前页面导航状态
  updateCurrentPage(currentPath) {
    const navLinks = document.querySelectorAll('nav a[href]');
    navLinks.forEach(link => {
      const href = link.getAttribute('href');
      if (href === currentPath) {
        link.classList.add('text-primary-600', 'dark:text-primary-400');
      } else {
        link.classList.remove('text-primary-600', 'dark:text-primary-400');
      }
    });
  }
}
