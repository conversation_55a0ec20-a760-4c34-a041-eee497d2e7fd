// 关于我们部分组件
import { siteData } from '../data/siteData.js';

export class AboutSection {
  constructor() {
    this.hasAnimated = false;
    this.init();
  }

  init() {
    this.render();
    this.setupIntersectionObserver();
  }

  render() {
    const aboutHTML = `
      <section class="py-20 bg-white dark:bg-gray-900 transition-colors duration-300">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <!-- 左侧：公司介绍 -->
            <div class="space-y-6">
              <div>
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                  关于我们
                </h2>
                <div class="w-20 h-1 bg-primary-600 mb-6"></div>
              </div>
              
              <p class="text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
                ${siteData.company.description}
              </p>
              
              <div class="space-y-4">
                <div class="flex items-start space-x-3">
                  <div class="flex-shrink-0 w-6 h-6 bg-primary-600 rounded-full flex items-center justify-center mt-1">
                    <svg class="w-3 h-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <div>
                    <h4 class="font-semibold text-gray-900 dark:text-white">专业团队</h4>
                    <p class="text-gray-600 dark:text-gray-300">拥有多年行业经验的专业技术团队</p>
                  </div>
                </div>
                
                <div class="flex items-start space-x-3">
                  <div class="flex-shrink-0 w-6 h-6 bg-primary-600 rounded-full flex items-center justify-center mt-1">
                    <svg class="w-3 h-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <div>
                    <h4 class="font-semibold text-gray-900 dark:text-white">品质保证</h4>
                    <p class="text-gray-600 dark:text-gray-300">严格的质量控制体系，确保产品品质</p>
                  </div>
                </div>
                
                <div class="flex items-start space-x-3">
                  <div class="flex-shrink-0 w-6 h-6 bg-primary-600 rounded-full flex items-center justify-center mt-1">
                    <svg class="w-3 h-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <div>
                    <h4 class="font-semibold text-gray-900 dark:text-white">服务至上</h4>
                    <p class="text-gray-600 dark:text-gray-300">以客户为中心，提供全方位优质服务</p>
                  </div>
                </div>
              </div>
              
              <div class="pt-4">
                <a href="/about" class="inline-flex items-center bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-300 transform hover:scale-105">
                  了解更多
                  <svg class="ml-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                  </svg>
                </a>
              </div>
            </div>

            <!-- 右侧：统计数据 -->
            <div class="grid grid-cols-2 gap-6">
              ${siteData.stats.map((stat, index) => `
                <div class="text-center p-6 bg-gray-50 dark:bg-gray-800 rounded-lg hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1" data-stat-card="${index}">
                  <div class="text-4xl md:text-5xl font-bold text-primary-600 mb-2" data-stat-number="${index}">
                    0
                  </div>
                  <div class="text-gray-600 dark:text-gray-300 font-medium">
                    ${stat.name}
                  </div>
                </div>
              `).join('')}
            </div>
          </div>
        </div>
      </section>
    `;

    return aboutHTML;
  }

  setupIntersectionObserver() {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting && !this.hasAnimated) {
          this.animateStats();
          this.hasAnimated = true;
        }
      });
    }, {
      threshold: 0.5
    });

    // 观察统计数据区域
    const statsSection = document.querySelector('[data-stat-card]')?.parentElement;
    if (statsSection) {
      observer.observe(statsSection);
    }
  }

  animateStats() {
    siteData.stats.forEach((stat, index) => {
      const element = document.querySelector(`[data-stat-number="${index}"]`);
      if (element) {
        const targetValue = parseInt(stat.value.replace(/\D/g, ''));
        const suffix = stat.suffix || '';
        
        // 数字动画
        let current = 0;
        const increment = targetValue / 60; // 60帧动画
        const timer = setInterval(() => {
          current += increment;
          if (current >= targetValue) {
            current = targetValue;
            clearInterval(timer);
          }
          
          // 格式化显示
          let displayValue = Math.floor(current).toString();
          if (stat.value.includes('+')) {
            displayValue += '+';
          }
          element.textContent = displayValue;
        }, 16); // 约60fps
      }
    });

    // 添加卡片动画
    const statCards = document.querySelectorAll('[data-stat-card]');
    statCards.forEach((card, index) => {
      setTimeout(() => {
        card.classList.add('animate-fade-in-up');
      }, index * 100);
    });
  }

  // 重置动画状态（用于页面重新加载时）
  resetAnimation() {
    this.hasAnimated = false;
    const statNumbers = document.querySelectorAll('[data-stat-number]');
    statNumbers.forEach(element => {
      element.textContent = '0';
    });
  }
}
