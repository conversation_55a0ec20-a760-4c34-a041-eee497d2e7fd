// Layout 布局组件
import { Header } from './Header.js';
import { Footer } from './Footer.js';

export class Layout {
  constructor() {
    this.header = null;
    this.footer = null;
    this.init();
  }

  init() {
    this.setupLayout();
    this.initializeComponents();
    this.setupScrollToTop();
  }

  setupLayout() {
    // 确保 body 有正确的类名
    document.body.classList.add('min-h-screen', 'bg-gray-50', 'dark:bg-gray-900', 'transition-colors', 'duration-300');
    
    // 添加主要内容容器
    if (!document.getElementById('main-content')) {
      const mainContent = document.createElement('main');
      mainContent.id = 'main-content';
      mainContent.className = 'pt-16'; // 为固定头部留出空间
      
      // 将现有内容移动到主容器中
      const existingContent = Array.from(document.body.children).filter(
        child => !['HEADER', 'FOOTER', 'SCRIPT'].includes(child.tagName)
      );
      
      existingContent.forEach(element => {
        mainContent.appendChild(element);
      });
      
      document.body.appendChild(mainContent);
    }
  }

  initializeComponents() {
    // 初始化 Header
    this.header = new Header();
    
    // 初始化 Footer
    this.footer = new Footer();
  }

  setupScrollToTop() {
    // 创建回到顶部按钮
    const scrollToTopHTML = `
      <button 
        id="scroll-to-top"
        class="fixed bottom-8 right-8 bg-primary-600 hover:bg-primary-700 text-white p-3 rounded-full shadow-lg opacity-0 invisible transition-all duration-300 z-50 group"
        aria-label="回到顶部"
      >
        <svg class="h-6 w-6 transform group-hover:-translate-y-1 transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
        </svg>
      </button>
    `;

    document.body.insertAdjacentHTML('beforeend', scrollToTopHTML);

    const scrollToTopButton = document.getElementById('scroll-to-top');
    
    // 监听滚动事件
    window.addEventListener('scroll', () => {
      if (window.pageYOffset > 300) {
        scrollToTopButton.classList.remove('opacity-0', 'invisible');
        scrollToTopButton.classList.add('opacity-100', 'visible');
      } else {
        scrollToTopButton.classList.add('opacity-0', 'invisible');
        scrollToTopButton.classList.remove('opacity-100', 'visible');
      }
    });

    // 点击回到顶部
    scrollToTopButton.addEventListener('click', () => {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    });
  }

  // 设置页面标题和描述
  setPageMeta(title, description = '', keywords = '') {
    document.title = title;
    
    // 设置或更新 meta 描述
    let metaDescription = document.querySelector('meta[name="description"]');
    if (!metaDescription) {
      metaDescription = document.createElement('meta');
      metaDescription.name = 'description';
      document.head.appendChild(metaDescription);
    }
    metaDescription.content = description;

    // 设置或更新 meta 关键词
    if (keywords) {
      let metaKeywords = document.querySelector('meta[name="keywords"]');
      if (!metaKeywords) {
        metaKeywords = document.createElement('meta');
        metaKeywords.name = 'keywords';
        document.head.appendChild(metaKeywords);
      }
      metaKeywords.content = keywords;
    }
  }

  // 显示加载状态
  showLoading() {
    const loadingHTML = `
      <div id="loading-overlay" class="fixed inset-0 bg-white dark:bg-gray-900 bg-opacity-80 dark:bg-opacity-80 flex items-center justify-center z-50">
        <div class="text-center">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p class="text-gray-600 dark:text-gray-400">加载中...</p>
        </div>
      </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', loadingHTML);
  }

  // 隐藏加载状态
  hideLoading() {
    const loadingOverlay = document.getElementById('loading-overlay');
    if (loadingOverlay) {
      loadingOverlay.remove();
    }
  }

  // 显示消息提示
  showMessage(message, type = 'info', duration = 3000) {
    const messageId = 'message-' + Date.now();
    const typeClasses = {
      success: 'bg-green-500 text-white',
      error: 'bg-red-500 text-white',
      warning: 'bg-yellow-500 text-white',
      info: 'bg-blue-500 text-white'
    };

    const messageHTML = `
      <div id="${messageId}" class="fixed top-20 right-4 ${typeClasses[type]} px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300">
        <div class="flex items-center space-x-2">
          <span>${message}</span>
          <button onclick="document.getElementById('${messageId}').remove()" class="ml-2 text-white hover:text-gray-200">
            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
    `;

    document.body.insertAdjacentHTML('beforeend', messageHTML);
    
    const messageElement = document.getElementById(messageId);
    
    // 显示动画
    setTimeout(() => {
      messageElement.classList.remove('translate-x-full');
    }, 100);

    // 自动隐藏
    setTimeout(() => {
      messageElement.classList.add('translate-x-full');
      setTimeout(() => {
        messageElement.remove();
      }, 300);
    }, duration);
  }

  // 更新当前页面导航状态
  updateCurrentPage(currentPath) {
    if (this.header) {
      this.header.updateCurrentPage(currentPath);
    }
  }

  // 添加面包屑导航
  addBreadcrumb(items) {
    const breadcrumbHTML = `
      <nav class="bg-gray-100 dark:bg-gray-800 py-3" aria-label="面包屑导航">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <ol class="flex items-center space-x-2 text-sm">
            ${items.map((item, index) => `
              <li class="flex items-center">
                ${index > 0 ? `
                  <svg class="h-4 w-4 text-gray-400 mx-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                  </svg>
                ` : ''}
                ${item.href ? `
                  <a href="${item.href}" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200">
                    ${item.name}
                  </a>
                ` : `
                  <span class="text-gray-900 dark:text-white font-medium">
                    ${item.name}
                  </span>
                `}
              </li>
            `).join('')}
          </ol>
        </div>
      </nav>
    `;

    const mainContent = document.getElementById('main-content');
    if (mainContent) {
      mainContent.insertAdjacentHTML('afterbegin', breadcrumbHTML);
    }
  }
}
