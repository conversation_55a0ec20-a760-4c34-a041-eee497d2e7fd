<!DOCTYPE html>
<html lang="zh-CN" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="重庆锦雨丰建筑有限公司 - 专业提供玻璃幕墙和铝合金门窗服务">
    <meta name="keywords" content="重庆建筑,玻璃幕墙,铝合金门窗,建筑幕墙,门窗定制,重庆锦雨丰">
    <title>重庆锦雨丰建筑有限公司 - 专业玻璃幕墙与铝合金门窗服务</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/images/logo/logo-black.png">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- 自定义样式 -->
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #64748b;
        }
        
        /* 导航栏样式 */
        .navbar-brand img {
            height: 40px;
        }
        
        /* 轮播图样式 */
        .hero-carousel {
            height: calc(100vh - 76px);
            min-height: 500px;
        }
        
        .hero-carousel .carousel-item {
            height: 100%;
        }
        
        .hero-carousel .carousel-item img {
            height: 100%;
            object-fit: cover;
        }
        
        .hero-overlay {
            background: rgba(0, 0, 0, 0.4);
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1;
        }
        
        .hero-content {
            position: relative;
            z-index: 2;
        }
        
        /* 主题切换按钮 */
        .theme-toggle {
            border: none;
            background: none;
            color: var(--bs-navbar-color);
            padding: 0.5rem;
            border-radius: 0.375rem;
            transition: all 0.3s ease;
        }
        
        .theme-toggle:hover {
            background-color: var(--bs-navbar-hover-color);
            color: var(--bs-navbar-active-color);
        }
        
        /* 暗黑模式样式 */
        [data-bs-theme="dark"] {
            --primary-color: #3b82f6;
            --primary-dark: #2563eb;
        }
        
        /* 动画效果 */
        .fade-in-up {
            animation: fadeInUp 0.8s ease-out;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* 自定义按钮样式 */
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
            border-color: var(--primary-dark);
        }
        
        /* 回到顶部按钮 */
        .back-to-top {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }
        
        .back-to-top.show {
            opacity: 1;
            visibility: visible;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light fixed-top shadow-sm">
        <div class="container">
            <!-- 品牌Logo -->
            <a class="navbar-brand" href="#">
                <img src="/images/logo/logo-black.png" alt="重庆锦雨丰建筑有限公司" class="d-inline-block align-text-top">
            </a>
            
            <!-- 移动端切换按钮 -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <!-- 导航菜单 -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#home">网站首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about">关于我们</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#products">我们的产品</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#cases">成功案例</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#videos">视频中心</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#news">资讯动态</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contact">联系我们</a>
                    </li>
                </ul>
                
                <!-- 右侧按钮组 -->
                <div class="d-flex align-items-center gap-2">
                    <!-- 主题切换按钮 -->
                    <button class="theme-toggle" id="themeToggle" title="切换主题">
                        <i class="bi bi-sun-fill" id="lightIcon"></i>
                        <i class="bi bi-moon-fill d-none" id="darkIcon"></i>
                    </button>
                    
                    <!-- 联系我们按钮 -->
                    <a href="#contact" class="btn btn-primary btn-sm">
                        <i class="bi bi-telephone me-1"></i>联系我们
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main>
        <!-- 轮播图区域 -->
        <section id="home" class="hero-carousel">
            <div id="heroCarousel" class="carousel slide carousel-fade h-100" data-bs-ride="carousel" data-bs-interval="5000">
                <!-- 轮播指示器 -->
                <div class="carousel-indicators">
                    <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="0" class="active"></button>
                    <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="1"></button>
                    <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="2"></button>
                </div>
                
                <!-- 轮播内容 -->
                <div class="carousel-inner h-100">
                    <!-- 第一张幻灯片 -->
                    <div class="carousel-item active h-100">
                        <img src="/images/banners/banners1.jpg" class="d-block w-100" alt="专业玻璃幕墙设计与施工">
                        <div class="hero-overlay"></div>
                        <div class="carousel-caption d-flex align-items-center justify-content-center h-100 hero-content">
                            <div class="text-center">
                                <h1 class="display-4 fw-bold mb-4 fade-in-up">专业玻璃幕墙设计与施工</h1>
                                <p class="lead mb-4 fade-in-up">打造现代化建筑外观，提升建筑品质</p>
                                <a href="#about" class="btn btn-primary btn-lg fade-in-up">
                                    <i class="bi bi-arrow-right me-2"></i>了解更多
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 第二张幻灯片 -->
                    <div class="carousel-item h-100">
                        <img src="/images/banners/banners2.jpg" class="d-block w-100" alt="高品质铝合金门窗定制">
                        <div class="hero-overlay"></div>
                        <div class="carousel-caption d-flex align-items-center justify-content-center h-100 hero-content">
                            <div class="text-center">
                                <h1 class="display-4 fw-bold mb-4 fade-in-up">高品质铝合金门窗定制</h1>
                                <p class="lead mb-4 fade-in-up">节能环保，安全耐用，为您打造舒适家居</p>
                                <a href="#products" class="btn btn-primary btn-lg fade-in-up">
                                    <i class="bi bi-grid-3x3-gap me-2"></i>查看产品
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 第三张幻灯片 -->
                    <div class="carousel-item h-100">
                        <img src="/images/banners/banners3.jpg" class="d-block w-100" alt="丰富的成功案例经验">
                        <div class="hero-overlay"></div>
                        <div class="carousel-caption d-flex align-items-center justify-content-center h-100 hero-content">
                            <div class="text-center">
                                <h1 class="display-4 fw-bold mb-4 fade-in-up">丰富的成功案例经验</h1>
                                <p class="lead mb-4 fade-in-up">多年行业经验，值得信赖的建筑合作伙伴</p>
                                <a href="#cases" class="btn btn-primary btn-lg fade-in-up">
                                    <i class="bi bi-collection me-2"></i>查看案例
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 轮播控制按钮 -->
                <button class="carousel-control-prev" type="button" data-bs-target="#heroCarousel" data-bs-slide="prev">
                    <span class="carousel-control-prev-icon"></span>
                    <span class="visually-hidden">上一张</span>
                </button>
                <button class="carousel-control-next" type="button" data-bs-target="#heroCarousel" data-bs-slide="next">
                    <span class="carousel-control-next-icon"></span>
                    <span class="visually-hidden">下一张</span>
                </button>
            </div>
        </section>

        <!-- 测试内容区域 -->
        <section id="about" class="py-5 bg-light">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8 mx-auto text-center">
                        <h2 class="display-5 fw-bold mb-4">Bootstrap 主题切换测试</h2>
                        <p class="lead mb-5">这个区域用于测试 Bootstrap 的主题切换功能。点击右上角的主题切换按钮，观察页面的变化。</p>
                    </div>
                </div>
                
                <div class="row g-4">
                    <div class="col-md-4">
                        <div class="card h-100 shadow-sm">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <i class="bi bi-building text-primary me-2"></i>专业服务
                                </h5>
                                <p class="card-text">提供专业的玻璃幕墙设计与施工服务，确保项目质量。</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card h-100 shadow-sm">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <i class="bi bi-award text-primary me-2"></i>品质保证
                                </h5>
                                <p class="card-text">严格的质量控制体系，为客户提供高品质的建筑解决方案。</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card h-100 shadow-sm">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <i class="bi bi-people text-primary me-2"></i>专业团队
                                </h5>
                                <p class="card-text">拥有经验丰富的专业团队，为项目成功提供有力保障。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 回到顶部按钮 -->
    <button class="btn btn-primary back-to-top" id="backToTop" title="回到顶部">
        <i class="bi bi-arrow-up"></i>
    </button>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 自定义JavaScript -->
    <script>
        // 主题切换功能
        function initThemeToggle() {
            const themeToggle = document.getElementById('themeToggle');
            const lightIcon = document.getElementById('lightIcon');
            const darkIcon = document.getElementById('darkIcon');
            const html = document.documentElement;
            
            // 获取当前主题
            function getCurrentTheme() {
                return localStorage.getItem('theme') || 
                       (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
            }
            
            // 应用主题
            function applyTheme(theme) {
                html.setAttribute('data-bs-theme', theme);
                localStorage.setItem('theme', theme);
                
                if (theme === 'dark') {
                    lightIcon.classList.add('d-none');
                    darkIcon.classList.remove('d-none');
                } else {
                    lightIcon.classList.remove('d-none');
                    darkIcon.classList.add('d-none');
                }
            }
            
            // 切换主题
            function toggleTheme() {
                const currentTheme = getCurrentTheme();
                const newTheme = currentTheme === 'light' ? 'dark' : 'light';
                applyTheme(newTheme);
            }
            
            // 初始化主题
            applyTheme(getCurrentTheme());
            
            // 绑定点击事件
            themeToggle.addEventListener('click', toggleTheme);
        }
        
        // 回到顶部功能
        function initBackToTop() {
            const backToTop = document.getElementById('backToTop');
            
            window.addEventListener('scroll', () => {
                if (window.pageYOffset > 300) {
                    backToTop.classList.add('show');
                } else {
                    backToTop.classList.remove('show');
                }
            });
            
            backToTop.addEventListener('click', () => {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        }
        
        // 平滑滚动
        function initSmoothScroll() {
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        const offsetTop = target.offsetTop - 76; // 减去导航栏高度
                        window.scrollTo({
                            top: offsetTop,
                            behavior: 'smooth'
                        });
                    }
                });
            });
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initThemeToggle();
            initBackToTop();
            initSmoothScroll();
        });
    </script>
</body>
</html>
