<!DOCTYPE html>
<html lang="zh-CN" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="重庆锦雨丰建筑有限公司 - 专业提供玻璃幕墙和铝合金门窗服务，致力于为客户提供高品质建筑服务的综合性企业">
    <meta name="keywords" content="重庆建筑,玻璃幕墙,铝合金门窗,建筑幕墙,门窗定制,重庆锦雨丰">
    <title>重庆锦雨丰建筑有限公司 - 专业玻璃幕墙与铝合金门窗服务</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/src/images/logo/logo-black.png">

    <!-- CSS -->
    <link rel="stylesheet" href="/src/style.css">

    <!-- 预加载关键资源 -->
    <link rel="preload" href="/src/images/banners/banners1.jpg" as="image">
    <link rel="preload" href="/src/images/logo/logo-black.png" as="image">
    <link rel="preload" href="/src/images/logo/logo-white.png" as="image">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="重庆锦雨丰建筑有限公司">
    <meta property="og:description" content="专业提供玻璃幕墙和铝合金门窗服务，致力于为客户提供高品质建筑服务">
    <meta property="og:image" content="/src/images/banners/banners1.jpg">
    <meta property="og:url" content="https://www.jinyufeng.com">
    <meta property="og:type" content="website">

    <!-- 结构化数据 -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Organization",
      "name": "重庆锦雨丰建筑有限公司",
      "url": "https://www.jinyufeng.com",
      "logo": "/src/images/logo/logo-black.png",
      "description": "专业提供玻璃幕墙和铝合金门窗服务的综合性建筑企业",
      "address": {
        "@type": "PostalAddress",
        "addressLocality": "重庆",
        "addressCountry": "CN"
      },
      "contactPoint": {
        "@type": "ContactPoint",
        "telephone": "023-12345678",
        "contactType": "customer service"
      }
    }
    </script>
</head>
<body class="antialiased">
    <!-- 页面加载指示器 -->
    <div id="page-loader" class="fixed inset-0 bg-white dark:bg-gray-900 z-50 flex items-center justify-center">
        <div class="text-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <p class="text-gray-600 dark:text-gray-400">加载中...</p>
        </div>
    </div>

    <!-- 主要内容将通过 JavaScript 动态插入 -->
    <main id="app" class="opacity-0 transition-opacity duration-500">
        <!-- 内容将在这里动态生成 -->
    </main>

    <!-- JavaScript 模块 -->
    <script src="/js/theme.js"></script>
    <script type="module">
        // 导入所有组件
        import { Layout } from '/components/Layout.js';
        import { HeroBanner } from '/components/HeroBanner.js';
        import { AboutSection } from '/components/AboutSection.js';
        import { ProductsSection } from '/components/ProductsSection.js';
        import { CasesSection } from '/components/CasesSection.js';
        import { VideosSection } from '/components/VideosSection.js';
        import { NewsSection } from '/components/NewsSection.js';
        import { ContactSection } from '/components/ContactSection.js';

        // 页面初始化
        class HomePage {
            constructor() {
                this.layout = null;
                this.components = [];
                this.init();
            }

            async init() {
                try {
                    // 设置页面元数据
                    this.setPageMeta();

                    // 初始化布局
                    this.layout = new Layout();

                    // 渲染页面内容
                    await this.renderContent();

                    // 初始化组件
                    this.initializeComponents();

                    // 设置页面完成状态
                    this.setPageReady();

                } catch (error) {
                    console.error('页面初始化失败:', error);
                    this.showError();
                }
            }

            setPageMeta() {
                this.layout.setPageMeta(
                    '重庆锦雨丰建筑有限公司 - 专业玻璃幕墙与铝合金门窗服务',
                    '专业提供玻璃幕墙和铝合金门窗服务，致力于为客户提供高品质建筑服务的综合性企业',
                    '重庆建筑,玻璃幕墙,铝合金门窗,建筑幕墙,门窗定制,重庆锦雨丰'
                );
            }

            async renderContent() {
                const app = document.getElementById('app');

                // 创建各个组件实例
                const heroBanner = new HeroBanner();
                const aboutSection = new AboutSection();
                const productsSection = new ProductsSection();
                const casesSection = new CasesSection();
                const videosSection = new VideosSection();
                const newsSection = new NewsSection();
                const contactSection = new ContactSection();

                // 渲染页面内容
                const content = \`
                    \${heroBanner.render()}
                    \${aboutSection.render()}
                    \${productsSection.render()}
                    \${casesSection.render()}
                    \${videosSection.render()}
                    \${newsSection.render()}
                    \${contactSection.render()}
                \`;

                app.innerHTML = content;

                // 保存组件引用
                this.components = [
                    heroBanner,
                    aboutSection,
                    productsSection,
                    casesSection,
                    videosSection,
                    newsSection,
                    contactSection
                ];
            }

            initializeComponents() {
                // 重新初始化需要事件监听的组件
                this.components.forEach(component => {
                    if (component.setupEventListeners) {
                        component.setupEventListeners();
                    }
                    if (component.setupIntersectionObserver) {
                        component.setupIntersectionObserver();
                    }
                });

                // 设置平滑滚动
                this.setupSmoothScroll();

                // 设置懒加载
                this.setupLazyLoading();
            }

            setupSmoothScroll() {
                document.addEventListener('click', (e) => {
                    const link = e.target.closest('a[href^="#"]');
                    if (link) {
                        e.preventDefault();
                        const targetId = link.getAttribute('href').substring(1);
                        const targetElement = document.getElementById(targetId);
                        if (targetElement) {
                            targetElement.scrollIntoView({
                                behavior: 'smooth',
                                block: 'start'
                            });
                        }
                    }
                });
            }

            setupLazyLoading() {
                const images = document.querySelectorAll('img[data-src]');
                const imageObserver = new IntersectionObserver((entries, observer) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            img.src = img.dataset.src;
                            img.classList.remove('lazy');
                            imageObserver.unobserve(img);
                        }
                    });
                });

                images.forEach(img => imageObserver.observe(img));
            }

            setPageReady() {
                // 隐藏加载指示器
                const loader = document.getElementById('page-loader');
                const app = document.getElementById('app');

                setTimeout(() => {
                    loader.style.opacity = '0';
                    app.classList.remove('opacity-0');

                    setTimeout(() => {
                        loader.remove();
                    }, 500);
                }, 1000);

                // 更新导航状态
                this.layout.updateCurrentPage('/');
            }

            showError() {
                const app = document.getElementById('app');
                app.innerHTML = \`
                    <div class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
                        <div class="text-center">
                            <h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">页面加载失败</h1>
                            <p class="text-gray-600 dark:text-gray-400 mb-6">抱歉，页面加载时出现了问题</p>
                            <button onclick="location.reload()" class="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200">
                                重新加载
                            </button>
                        </div>
                    </div>
                \`;

                const loader = document.getElementById('page-loader');
                const appElement = document.getElementById('app');
                loader.style.opacity = '0';
                appElement.classList.remove('opacity-0');
                setTimeout(() => loader.remove(), 500);
            }
        }

        // 初始化页面
        new HomePage();
    </script>
</body>
</html>
