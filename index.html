<!DOCTYPE html>
<html lang="zh-CN" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="重庆锦雨丰建筑有限公司 - 专业提供玻璃幕墙和铝合金门窗服务，致力于为客户提供高品质建筑服务的综合性企业">
    <meta name="keywords" content="重庆建筑,玻璃幕墙,铝合金门窗,建筑幕墙,门窗定制,重庆锦雨丰">
    <title>重庆锦雨丰建筑有限公司 - 专业玻璃幕墙与铝合金门窗服务</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/src/images/logo/logo-black.png">

    <!-- CSS -->
    <link rel="stylesheet" href="/src/style.css">

    <!-- 预加载关键资源 -->
    <link rel="preload" href="/src/images/banners/banners1.jpg" as="image">
    <link rel="preload" href="/src/images/logo/logo-black.png" as="image">
    <link rel="preload" href="/src/images/logo/logo-white.png" as="image">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="重庆锦雨丰建筑有限公司">
    <meta property="og:description" content="专业提供玻璃幕墙和铝合金门窗服务，致力于为客户提供高品质建筑服务">
    <meta property="og:image" content="/src/images/banners/banners1.jpg">
    <meta property="og:url" content="https://www.jinyufeng.com">
    <meta property="og:type" content="website">

    <!-- 结构化数据 -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Organization",
      "name": "重庆锦雨丰建筑有限公司",
      "url": "https://www.jinyufeng.com",
      "logo": "/src/images/logo/logo-black.png",
      "description": "专业提供玻璃幕墙和铝合金门窗服务的综合性建筑企业",
      "address": {
        "@type": "PostalAddress",
        "addressLocality": "重庆",
        "addressCountry": "CN"
      },
      "contactPoint": {
        "@type": "ContactPoint",
        "telephone": "023-12345678",
        "contactType": "customer service"
      }
    }
    </script>
</head>
<body class="antialiased">
    <!-- 页面加载指示器 -->
    <div id="page-loader" class="fixed inset-0 bg-white dark:bg-gray-900 z-50 flex items-center justify-center">
        <div class="text-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <p class="text-gray-600 dark:text-gray-400">加载中...</p>
        </div>
    </div>

    <!-- 主要内容将通过 JavaScript 动态插入 -->
    <main id="app" class="opacity-0 transition-opacity duration-500">
        <!-- 内容将在这里动态生成 -->
    </main>

    <!-- JavaScript 模块 -->
    <script src="/js/theme.js"></script>
    <script type="module">
        // 简单的测试页面
        document.addEventListener('DOMContentLoaded', function() {
            const app = document.getElementById('app');
            const loader = document.getElementById('page-loader');

            // 简单的测试内容
            app.innerHTML = `
                <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
                    <!-- Header -->
                    <header class="bg-white dark:bg-gray-800 shadow-lg">
                        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                            <div class="flex justify-between items-center h-16">
                                <div class="flex items-center">
                                    <img class="h-8 w-auto" src="/src/images/logo/logo-black.png" alt="重庆锦雨丰建筑有限公司">
                                    <span class="ml-3 text-xl font-bold text-gray-900 dark:text-white">重庆锦雨丰建筑有限公司</span>
                                </div>
                                <button
                                    data-theme-toggle
                                    class="p-2 rounded-md text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700"
                                >
                                    <svg data-theme-icon="sun" class="h-5 w-5 hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                                    </svg>
                                    <svg data-theme-icon="moon" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </header>

                    <!-- Hero Section -->
                    <section class="relative h-screen">
                        <img src="/src/images/banners/banners1.jpg" alt="专业玻璃幕墙设计与施工" class="w-full h-full object-cover">
                        <div class="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
                            <div class="text-center text-white max-w-4xl mx-auto px-4">
                                <h1 class="text-4xl md:text-6xl font-bold mb-6">专业玻璃幕墙设计与施工</h1>
                                <p class="text-xl md:text-2xl mb-8 opacity-90">打造现代化建筑外观，提升建筑品质</p>
                                <a href="#" class="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-colors duration-300">
                                    了解更多
                                    <svg class="ml-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </section>

                    <!-- About Section -->
                    <section class="py-20 bg-white dark:bg-gray-800">
                        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                            <div class="text-center mb-16">
                                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">关于我们</h2>
                                <div class="w-20 h-1 bg-blue-600 mx-auto mb-6"></div>
                                <p class="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                    一家致力于为客户提供高品质建筑服务的综合性企业。我们秉承"诚信经营、质量为本、客户至上、合作共赢"的经营理念，在激烈的市场竞争中不断发展壮大，已成为重庆地区颇具实力的建筑企业之一。
                                </p>
                            </div>

                            <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
                                <div class="text-center">
                                    <div class="text-4xl font-bold text-blue-600 mb-2">2010</div>
                                    <div class="text-gray-600 dark:text-gray-300">成立年份</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-4xl font-bold text-blue-600 mb-2">500+</div>
                                    <div class="text-gray-600 dark:text-gray-300">完成项目</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-4xl font-bold text-blue-600 mb-2">1000+</div>
                                    <div class="text-gray-600 dark:text-gray-300">服务客户</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-4xl font-bold text-blue-600 mb-2">50</div>
                                    <div class="text-gray-600 dark:text-gray-300">团队成员</div>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
            `;

            // 隐藏加载器
            setTimeout(() => {
                loader.style.opacity = '0';
                app.classList.remove('opacity-0');
                setTimeout(() => loader.remove(), 500);
            }, 1000);
        });
    </script>
</body>
</html>
