<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试页面</title>
    <link rel="stylesheet" href="/src/style.css">
</head>
<body>
    <h1>图片加载测试</h1>
    
    <div style="margin: 20px 0;">
        <h2>轮播图片测试</h2>
        <img src="/src/images/banners/banners1.jpg" alt="图片1" style="width: 300px; height: 200px; object-fit: cover; border: 1px solid red;">
        <img src="/src/images/banners/banners2.jpg" alt="图片2" style="width: 300px; height: 200px; object-fit: cover; border: 1px solid red;">
        <img src="/src/images/banners/banners3.jpg" alt="图片3" style="width: 300px; height: 200px; object-fit: cover; border: 1px solid red;">
    </div>
    
    <div style="margin: 20px 0;">
        <h2>Logo测试</h2>
        <img src="/src/images/logo/logo-black.png" alt="黑色Logo" style="height: 50px; border: 1px solid red;">
        <img src="/src/images/logo/logo-white.png" alt="白色Logo" style="height: 50px; border: 1px solid red; background: black;">
    </div>
    
    <div style="margin: 20px 0;">
        <h2>主题切换测试</h2>
        <button id="theme-toggle" style="padding: 10px; border: 1px solid #ccc;">
            <span id="theme-text">切换主题</span>
        </button>
        <div id="theme-status" style="margin-top: 10px; padding: 10px; border: 1px solid #ccc;">
            当前主题: <span id="current-theme">未知</span>
        </div>
    </div>
    
    <script>
        // 主题切换测试
        const themeToggle = document.getElementById('theme-toggle');
        const themeStatus = document.getElementById('current-theme');
        const html = document.documentElement;
        
        function getCurrentTheme() {
            return localStorage.getItem('theme') || 
                   (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
        }
        
        function applyTheme(theme) {
            console.log('应用主题:', theme);
            if (theme === 'dark') {
                html.classList.add('dark');
                document.body.style.backgroundColor = '#1f2937';
                document.body.style.color = '#ffffff';
            } else {
                html.classList.remove('dark');
                document.body.style.backgroundColor = '#ffffff';
                document.body.style.color = '#000000';
            }
            localStorage.setItem('theme', theme);
            themeStatus.textContent = theme;
        }
        
        function toggleTheme() {
            const currentTheme = getCurrentTheme();
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            console.log('切换主题从', currentTheme, '到', newTheme);
            applyTheme(newTheme);
        }
        
        // 初始化
        applyTheme(getCurrentTheme());
        themeToggle.addEventListener('click', toggleTheme);
        
        // 图片加载测试
        document.querySelectorAll('img').forEach((img, index) => {
            img.onload = () => console.log(`图片 ${index + 1} 加载成功:`, img.src);
            img.onerror = () => console.error(`图片 ${index + 1} 加载失败:`, img.src);
        });
    </script>
</body>
</html>
