/* 重庆锦雨丰建筑有限公司 - 自定义样式 */

/* ===== CSS 变量定义 ===== */
:root {
    /* 主色调 - 建筑行业蓝色调 */
    --bs-primary: #2563eb;
    --bs-primary-rgb: 37, 99, 235;
    --primary-dark: #1d4ed8;
    --primary-light: #3b82f6;
    
    /* 辅助色彩 */
    --bs-secondary: #64748b;
    --bs-secondary-rgb: 100, 116, 139;
    
    /* 成功色 - 环保绿 */
    --bs-success: #059669;
    --bs-success-rgb: 5, 150, 105;
    
    /* 警告色 */
    --bs-warning: #d97706;
    --bs-warning-rgb: 217, 119, 6;
    
    /* 危险色 */
    --bs-danger: #dc2626;
    --bs-danger-rgb: 220, 38, 38;
    
    /* 信息色 */
    --bs-info: #0891b2;
    --bs-info-rgb: 8, 145, 178;
    
    /* 灰色调 */
    --bs-gray-50: #f8fafc;
    --bs-gray-100: #f1f5f9;
    --bs-gray-200: #e2e8f0;
    --bs-gray-300: #cbd5e1;
    --bs-gray-400: #94a3b8;
    --bs-gray-500: #64748b;
    --bs-gray-600: #475569;
    --bs-gray-700: #334155;
    --bs-gray-800: #1e293b;
    --bs-gray-900: #0f172a;
    
    /* 阴影效果 */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* 过渡动画 */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* 暗黑模式变量 */
[data-bs-theme="dark"] {
    --bs-primary: #3b82f6;
    --bs-primary-rgb: 59, 130, 246;
    --primary-dark: #2563eb;
    --primary-light: #60a5fa;

    /* 背景色重新定义 */
    --bs-light: #374151;
    --bs-light-rgb: 55, 65, 81;
    --bs-dark: #f8fafc;
    --bs-dark-rgb: 248, 250, 252;
}

/* 暗黑模式下的背景色修复 */
[data-bs-theme="dark"] .bg-light {
    background-color: var(--bs-gray-800) !important;
}

[data-bs-theme="dark"] .bg-dark {
    background-color: var(--bs-gray-100) !important;
    color: var(--bs-gray-900) !important;
}

/* ===== 基础样式 ===== */
body {
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
    line-height: 1.6;
    scroll-behavior: smooth;
}

/* 导航栏样式 */
.navbar {
    backdrop-filter: blur(10px);
    transition: all var(--transition-normal);
}

.navbar-brand img {
    height: 40px;
    transition: all var(--transition-fast);
}

.navbar-brand img:hover {
    transform: scale(1.05);
}

.navbar-nav .nav-link {
    font-weight: 500;
    padding: 0.75rem 1rem;
    transition: all var(--transition-fast);
    position: relative;
}

.navbar-nav .nav-link:hover {
    color: var(--bs-primary) !important;
}

.navbar-nav .nav-link.active {
    color: var(--bs-primary) !important;
    font-weight: 600;
}

/* 主题切换按钮 */
.theme-toggle {
    border: none;
    background: none;
    color: var(--bs-navbar-color);
    padding: 0.5rem;
    border-radius: 0.375rem;
    transition: all var(--transition-fast);
    font-size: 1.1rem;
}

.theme-toggle:hover {
    background-color: var(--bs-gray-100);
    color: var(--bs-primary);
    transform: scale(1.1);
}

[data-bs-theme="dark"] .theme-toggle:hover {
    background-color: var(--bs-gray-800);
}

/* ===== 轮播图样式 ===== */
.hero-carousel {
    height: 100vh;
    min-height: 600px;
    position: relative;
}

.hero-carousel .carousel-inner {
    height: 100%;
}

.hero-carousel .carousel-item {
    height: 100%;
    position: relative;
}

.hero-carousel .carousel-item img {
    height: 100%;
    width: 100%;
    object-fit: cover;
    filter: brightness(0.8);
    position: absolute;
    top: 0;
    left: 0;
}

.hero-overlay {
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.3) 0%, rgba(0, 0, 0, 0.4) 100%);
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.carousel-caption {
    bottom: 0;
    top: 0;
    padding-top: 0;
    padding-bottom: 0;
}

.carousel-control-prev,
.carousel-control-next {
    width: 5%;
    opacity: 0.8;
    transition: all var(--transition-fast);
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
    opacity: 1;
}

.carousel-indicators [data-bs-target] {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin: 0 4px;
    transition: all var(--transition-fast);
}

/* ===== 动画效果 ===== */
.fade-in-up {
    animation: fadeInUp 0.8s ease-out;
}

.fade-in-left {
    animation: fadeInLeft 0.8s ease-out;
}

.fade-in-right {
    animation: fadeInRight 0.8s ease-out;
}

.scale-on-hover {
    transition: transform var(--transition-normal);
}

.scale-on-hover:hover {
    transform: scale(1.05);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* ===== 卡片样式 ===== */
.card {
    border: none;
    box-shadow: var(--shadow);
    transition: all var(--transition-normal);
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-5px);
}

.card-img-top {
    transition: all var(--transition-normal);
}

.card:hover .card-img-top {
    transform: scale(1.05);
}

/* 特色卡片 */
.feature-card {
    text-align: center;
    padding: 2rem;
    border-radius: 1rem;
    background: var(--bs-body-bg);
    border: 1px solid var(--bs-border-color);
    transition: all var(--transition-normal);
}

.feature-card:hover {
    border-color: var(--bs-primary);
    box-shadow: var(--shadow-lg);
}

.feature-card .icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    background: linear-gradient(135deg, var(--bs-primary), var(--primary-light));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
}

/* ===== 统计数据样式 ===== */
.stats-card {
    text-align: center;
    padding: 2rem 1rem;
}

.stats-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--bs-primary);
    line-height: 1;
    margin-bottom: 0.5rem;
}

.stats-label {
    font-size: 1.1rem;
    color: var(--bs-secondary);
    font-weight: 500;
}

/* ===== 按钮样式 ===== */
.btn {
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    transition: all var(--transition-fast);
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, var(--bs-primary), var(--primary-light));
    border: none;
    box-shadow: var(--shadow);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--bs-primary));
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.btn-outline-primary {
    border: 2px solid var(--bs-primary);
    color: var(--bs-primary);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--bs-primary);
    border-color: var(--bs-primary);
    transform: translateY(-2px);
}

/* ===== 回到顶部按钮 ===== */
.back-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    z-index: 1000;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-lg);
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
    .hero-carousel {
        height: 100vh;
        min-height: 400px;
    }
    
    .stats-number {
        font-size: 2.5rem;
    }
    
    .feature-card {
        padding: 1.5rem;
    }
    
    .feature-card .icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
}

/* ===== 工具类 ===== */
.text-gradient {
    background: linear-gradient(135deg, var(--bs-primary), var(--primary-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--bs-primary), var(--primary-light));
}

.border-primary-soft {
    border: 1px solid rgba(var(--bs-primary-rgb), 0.2);
}

.shadow-soft {
    box-shadow: var(--shadow);
}

.shadow-hover {
    transition: box-shadow var(--transition-normal);
}

.shadow-hover:hover {
    box-shadow: var(--shadow-lg);
}
