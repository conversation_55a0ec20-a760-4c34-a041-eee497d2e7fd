// 资讯动态部分组件
import { newsData } from '../data/contentData.js';
import { formatDate, truncateText } from '../js/utils.js';

export class NewsSection {
  constructor() {
    this.init();
  }

  init() {
    this.render();
  }

  render() {
    const newsHTML = `
      <section class="py-20 bg-white dark:bg-gray-900 transition-colors duration-300">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <!-- 标题部分 -->
          <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              资讯动态
            </h2>
            <div class="w-20 h-1 bg-primary-600 mx-auto mb-6"></div>
            <p class="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              了解行业最新动态，掌握公司发展资讯
            </p>
          </div>

          <!-- 资讯展示 -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            ${newsData.slice(0, 6).map(news => `
              <article class="group bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden transform hover:-translate-y-2">
                <div class="relative overflow-hidden">
                  <img 
                    src="${news.image}" 
                    alt="${news.title}"
                    class="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-700"
                  >
                  <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-500"></div>
                  
                  <!-- 分类标签 -->
                  <div class="absolute top-4 left-4">
                    <span class="px-3 py-1 bg-primary-600 text-white text-sm rounded-full font-medium">
                      ${news.category}
                    </span>
                  </div>
                  
                  <!-- 发布日期 -->
                  <div class="absolute bottom-4 right-4 bg-white bg-opacity-90 text-gray-900 px-3 py-1 rounded-full text-sm font-medium">
                    ${this.formatDate(news.publishDate)}
                  </div>
                </div>
                
                <div class="p-6">
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-300 line-clamp-2">
                    <a href="/news/${news.id}" class="hover:underline">
                      ${news.title}
                    </a>
                  </h3>
                  
                  <p class="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-3 leading-relaxed">
                    ${news.summary}
                  </p>
                  
                  <div class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-4">
                    <div class="flex items-center space-x-4">
                      <span class="flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                        ${news.author}
                      </span>
                      <span class="flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        ${news.views}
                      </span>
                    </div>
                  </div>
                  
                  <!-- 标签 -->
                  <div class="flex flex-wrap gap-2 mb-4">
                    ${news.tags.slice(0, 3).map(tag => `
                      <span class="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-xs rounded-md">
                        #${tag}
                      </span>
                    `).join('')}
                  </div>
                  
                  <!-- 阅读更多链接 -->
                  <div class="pt-2 border-t border-gray-200 dark:border-gray-700">
                    <a 
                      href="/news/${news.id}" 
                      class="inline-flex items-center text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 text-sm font-medium transition-colors duration-200 group"
                    >
                      阅读全文
                      <svg class="ml-1 h-4 w-4 transform group-hover:translate-x-1 transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                      </svg>
                    </a>
                  </div>
                </div>
              </article>
            `).join('')}
          </div>

          <!-- 查看更多按钮 -->
          <div class="text-center">
            <a 
              href="/news" 
              class="inline-flex items-center bg-primary-600 hover:bg-primary-700 text-white px-8 py-4 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
            >
              查看更多资讯
              <svg class="ml-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </a>
          </div>
        </div>
      </section>
    `;

    return newsHTML;
  }

  formatDate(dateString) {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${month}-${day}`;
  }
}
