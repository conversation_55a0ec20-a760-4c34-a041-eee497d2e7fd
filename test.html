<!DOCTYPE html>
<html lang="zh-CN" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重庆锦雨丰建筑有限公司 - 测试页面</title>
    <link rel="stylesheet" href="/src/style.css">
</head>
<body class="antialiased">
    <!-- 页面加载指示器 -->
    <div id="page-loader" class="fixed inset-0 bg-white dark:bg-gray-900 z-50 flex items-center justify-center">
        <div class="text-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <p class="text-gray-600 dark:text-gray-400">加载中...</p>
        </div>
    </div>

    <!-- 主要内容 -->
    <main id="app" class="opacity-0 transition-opacity duration-500">
        <!-- 内容将在这里动态生成 -->
    </main>

    <!-- JavaScript -->
    <script src="/js/theme.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const app = document.getElementById('app');
            const loader = document.getElementById('page-loader');
            
            // 渲染页面内容
            app.innerHTML = `
                <!-- Header -->
                <header class="bg-white dark:bg-gray-900 shadow-lg fixed w-full top-0 z-50 transition-colors duration-300">
                    <nav class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div class="flex justify-between items-center h-16">
                            <!-- Logo 和公司名称 -->
                            <div class="flex-shrink-0 flex items-center">
                                <img class="h-10 w-auto dark:hidden" src="/src/images/logo/logo-black.png" alt="重庆锦雨丰建筑有限公司">
                                <img class="h-10 w-auto hidden dark:block" src="/src/images/logo/logo-white.png" alt="重庆锦雨丰建筑有限公司">
                                <span class="text-xl font-bold text-gray-900 dark:text-white hidden sm:block ml-3">
                                    重庆锦雨丰建筑有限公司
                                </span>
                            </div>

                            <!-- 桌面端导航菜单 -->
                            <div class="hidden md:block">
                                <div class="ml-10 flex items-baseline space-x-4">
                                    <a href="#" class="text-primary-600 dark:text-primary-400 px-3 py-2 rounded-md text-sm font-medium">网站首页</a>
                                    <a href="#about" class="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200">关于我们</a>
                                    <a href="#products" class="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200">我们的产品</a>
                                    <a href="#" class="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200">成功案例</a>
                                    <a href="#" class="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200">联系我们</a>
                                </div>
                            </div>

                            <!-- 右侧按钮组 -->
                            <div class="flex items-center space-x-4">
                                <!-- 主题切换按钮 -->
                                <button
                                    id="theme-toggle"
                                    class="p-2 rounded-md text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200"
                                    aria-label="切换主题"
                                >
                                    <svg id="sun-icon" class="h-5 w-5 hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                                    </svg>
                                    <svg id="moon-icon" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                                    </svg>
                                </button>

                                <!-- 联系我们按钮 -->
                                <a href="#contact" class="hidden md:inline-flex bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200">
                                    联系我们
                                </a>
                            </div>
                        </div>
                    </nav>
                </header>

                <!-- 主要内容区域 -->
                <div class="pt-16">
                    <!-- Hero Section -->
                    <section class="relative h-screen overflow-hidden">
                        <div class="relative h-full">
                            <div class="absolute inset-0">
                                <div class="absolute inset-0 bg-black bg-opacity-40 z-10"></div>
                                <img 
                                    src="/src/images/banners/banners1.jpg" 
                                    alt="专业玻璃幕墙设计与施工"
                                    class="w-full h-full object-cover"
                                >
                                <div class="absolute inset-0 flex items-center justify-center z-20">
                                    <div class="text-center text-white max-w-4xl mx-auto px-4">
                                        <h1 class="text-4xl md:text-6xl font-bold mb-6 animate-fade-in-up">
                                            专业玻璃幕墙设计与施工
                                        </h1>
                                        <p class="text-xl md:text-2xl mb-8 opacity-90 animate-fade-in-up" style="animation-delay: 0.2s;">
                                            打造现代化建筑外观，提升建筑品质
                                        </p>
                                        <div class="animate-fade-in-up" style="animation-delay: 0.4s;">
                                            <a 
                                                href="#about" 
                                                class="inline-flex items-center bg-primary-600 hover:bg-primary-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
                                            >
                                                了解更多
                                                <svg class="ml-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                                </svg>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- About Section -->
                    <section id="about" class="py-20 bg-white dark:bg-gray-900 transition-colors duration-300">
                        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                                <div class="space-y-6">
                                    <div>
                                        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                                            关于我们
                                        </h2>
                                        <div class="w-20 h-1 bg-primary-600 mb-6"></div>
                                    </div>
                                    
                                    <p class="text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
                                        一家致力于为客户提供高品质建筑服务的综合性企业。我们秉承"诚信经营、质量为本、客户至上、合作共赢"的经营理念，在激烈的市场竞争中不断发展壮大，已成为重庆地区颇具实力的建筑企业之一。
                                    </p>
                                    
                                    <div class="pt-4">
                                        <a href="#" class="inline-flex items-center bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-300 transform hover:scale-105">
                                            了解更多
                                            <svg class="ml-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                            </svg>
                                        </a>
                                    </div>
                                </div>

                                <div class="grid grid-cols-2 gap-6">
                                    <div class="text-center p-6 bg-gray-50 dark:bg-gray-800 rounded-lg hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                                        <div class="text-4xl md:text-5xl font-bold text-primary-600 mb-2">2010</div>
                                        <div class="text-gray-600 dark:text-gray-300 font-medium">成立年份</div>
                                    </div>
                                    <div class="text-center p-6 bg-gray-50 dark:bg-gray-800 rounded-lg hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                                        <div class="text-4xl md:text-5xl font-bold text-primary-600 mb-2">500+</div>
                                        <div class="text-gray-600 dark:text-gray-300 font-medium">完成项目</div>
                                    </div>
                                    <div class="text-center p-6 bg-gray-50 dark:bg-gray-800 rounded-lg hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                                        <div class="text-4xl md:text-5xl font-bold text-primary-600 mb-2">1000+</div>
                                        <div class="text-gray-600 dark:text-gray-300 font-medium">服务客户</div>
                                    </div>
                                    <div class="text-center p-6 bg-gray-50 dark:bg-gray-800 rounded-lg hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                                        <div class="text-4xl md:text-5xl font-bold text-primary-600 mb-2">50</div>
                                        <div class="text-gray-600 dark:text-gray-300 font-medium">团队成员</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- Products Section -->
                    <section id="products" class="py-20 bg-gray-50 dark:bg-gray-800 transition-colors duration-300">
                        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                            <div class="text-center mb-16">
                                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                                    我们的产品
                                </h2>
                                <div class="w-20 h-1 bg-primary-600 mx-auto mb-6"></div>
                                <p class="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                    专业提供玻璃幕墙和铝合金门窗产品，以卓越的品质和创新的设计，为现代建筑提供完美的解决方案
                                </p>
                            </div>

                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                                <div class="group relative overflow-hidden rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
                                    <div class="relative">
                                        <img 
                                            src="/src/images/glass-curtain-wall/glass-curtain-wall1.jpg" 
                                            alt="玻璃幕墙"
                                            class="w-full h-80 object-cover group-hover:scale-110 transition-transform duration-700"
                                        >
                                        <div class="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-60"></div>
                                    </div>
                                    
                                    <div class="absolute bottom-0 left-0 right-0 p-8 text-white">
                                        <h3 class="text-2xl font-bold mb-3">玻璃幕墙</h3>
                                        <p class="text-gray-200 mb-4">
                                            现代建筑的完美外衣，集美观、节能、安全于一体的专业幕墙系统
                                        </p>
                                        <a 
                                            href="#" 
                                            class="inline-flex items-center bg-white text-primary-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-all duration-300"
                                        >
                                            了解详情
                                            <svg class="ml-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                            </svg>
                                        </a>
                                    </div>
                                </div>

                                <div class="group relative overflow-hidden rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
                                    <div class="relative">
                                        <img 
                                            src="/src/images/aluminium-window/aluminium-window1.jpg" 
                                            alt="铝合金门窗"
                                            class="w-full h-80 object-cover group-hover:scale-110 transition-transform duration-700"
                                        >
                                        <div class="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-60"></div>
                                    </div>
                                    
                                    <div class="absolute bottom-0 left-0 right-0 p-8 text-white">
                                        <h3 class="text-2xl font-bold mb-3">铝合金门窗</h3>
                                        <p class="text-gray-200 mb-4">
                                            高品质门窗定制，为您的家居和办公空间提供安全、美观、节能的解决方案
                                        </p>
                                        <a 
                                            href="#" 
                                            class="inline-flex items-center bg-white text-primary-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-all duration-300"
                                        >
                                            了解详情
                                            <svg class="ml-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>

                <!-- 回到顶部按钮 -->
                <button 
                    id="scroll-to-top"
                    class="fixed bottom-8 right-8 bg-primary-600 hover:bg-primary-700 text-white p-3 rounded-full shadow-lg opacity-0 invisible transition-all duration-300 z-50 group"
                >
                    <svg class="h-6 w-6 transform group-hover:-translate-y-1 transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                    </svg>
                </button>
            `;
            
            // 主题切换功能
            function initTheme() {
                const themeToggle = document.getElementById('theme-toggle');
                const sunIcon = document.getElementById('sun-icon');
                const moonIcon = document.getElementById('moon-icon');
                const html = document.documentElement;

                // 获取当前主题
                function getCurrentTheme() {
                    return localStorage.getItem('theme') ||
                           (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                }

                // 应用主题
                function applyTheme(theme) {
                    if (theme === 'dark') {
                        html.classList.add('dark');
                        sunIcon.classList.remove('hidden');
                        moonIcon.classList.add('hidden');
                    } else {
                        html.classList.remove('dark');
                        sunIcon.classList.add('hidden');
                        moonIcon.classList.remove('hidden');
                    }
                    localStorage.setItem('theme', theme);
                }

                // 切换主题
                function toggleTheme() {
                    const currentTheme = getCurrentTheme();
                    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
                    applyTheme(newTheme);
                }

                // 初始化主题
                applyTheme(getCurrentTheme());

                // 绑定点击事件
                themeToggle.addEventListener('click', toggleTheme);

                // 监听系统主题变化
                window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
                    if (!localStorage.getItem('theme')) {
                        applyTheme(e.matches ? 'dark' : 'light');
                    }
                });
            }

            // 初始化主题
            initTheme();

            // 设置平滑滚动
            document.addEventListener('click', (e) => {
                const link = e.target.closest('a[href^="#"]');
                if (link) {
                    e.preventDefault();
                    const targetId = link.getAttribute('href').substring(1);
                    const targetElement = document.getElementById(targetId);
                    if (targetElement) {
                        targetElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                }
            });

            // 回到顶部按钮
            const scrollToTopButton = document.getElementById('scroll-to-top');
            window.addEventListener('scroll', () => {
                if (window.pageYOffset > 300) {
                    scrollToTopButton.classList.remove('opacity-0', 'invisible');
                    scrollToTopButton.classList.add('opacity-100', 'visible');
                } else {
                    scrollToTopButton.classList.add('opacity-0', 'invisible');
                    scrollToTopButton.classList.remove('opacity-100', 'visible');
                }
            });

            scrollToTopButton.addEventListener('click', () => {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
            
            // 隐藏加载器
            setTimeout(() => {
                loader.style.opacity = '0';
                app.classList.remove('opacity-0');
                setTimeout(() => loader.remove(), 500);
            }, 1000);
        });
    </script>
</body>
</html>
